# DysplaciaNet Model Analysis Setup

This repository contains scripts and setup tools for analyzing the DysplaciaNet model, a deep learning model for classifying dysplastic vs normal cells.

## 🚀 Quick Start

### Prerequisites
- Python 3.6+ (Python 3.7-3.8 recommended for best compatibility)
- Virtual environment support (`venv`)
- Git (if cloning the repository)

### Automatic Setup

1. **Run the setup script:**
   ```bash
   python setup_environment.py
   ```

2. **Activate the environment:**
   ```bash
   source dysplacianet_env/bin/activate
   ```

3. **Run the analysis scripts:**
   ```bash
   # Analyze the model
   python model_loader.py
   
   # Analyze the dataset
   python dataset_visualizer.py
   ```

### Manual Setup

If you prefer to set up manually:

1. **Create virtual environment:**
   ```bash
   python3 -m venv dysplacianet_env
   source dysplacianet_env/bin/activate
   ```

2. **Install dependencies:**
   ```bash
   pip install --upgrade pip
   pip install -r requirements_dysplacianet.txt
   ```

## 📁 Project Structure

```
.
├── DysplaciaNet_Interpretability_Study-master/
│   └── model/
│       ├── final_model.json          # Model architecture
│       └── final_model_weights.h5    # Model weights
├── data/                             # Dataset images and annotations
│   ├── *.jpg                        # Cell images
│   └── *_json.txt                   # Annotation files
├── model_loader.py                   # Model analysis script
├── dataset_visualizer.py             # Dataset analysis script
├── requirements_dysplacianet.txt     # Python dependencies
├── setup_environment.py              # Automated setup script
└── README_DysplaciaNet_Setup.md      # This file
```

## 🧠 Model Analysis (`model_loader.py`)

This script provides comprehensive analysis of the DysplaciaNet model:

### Features:
- **Model Loading**: Loads model from JSON architecture and H5 weights
- **Architecture Analysis**: Extracts layer information, parameters, and structure
- **Metrics Extraction**: Computes model statistics and performance metrics
- **Report Generation**: Creates detailed analysis reports

### Output:
- Console output with model metrics
- `dysplacianet_metrics.json` file with detailed metrics

### Example Usage:
```python
from model_loader import DysplaciaNetModelLoader

loader = DysplaciaNetModelLoader()
loader.load_model()
loader.extract_basic_metrics()
loader.extract_layer_metrics()
loader.print_comprehensive_metrics()
```

## 📊 Dataset Analysis (`dataset_visualizer.py`)

This script provides comprehensive analysis of the DysplaciaNet dataset:

### Features:
- **Dataset Loading**: Loads images and corresponding annotations
- **Classification Analysis**: Analyzes prediction accuracy and class distributions
- **Statistical Reports**: Generates detailed dataset statistics
- **Sample Information**: Displays information about sample images

### Dataset Categories:
- **TD**: Tesis Displasica (Dysplastic)
- **TN**: Tesis Normal (Normal)
- **WD**: Wrong Classification - Actually Dysplastic
- **WN**: Wrong Classification - Actually Normal
- **ZD**: Correct Classification - Dysplastic
- **ZN**: Correct Classification - Normal

### Example Usage:
```python
from dataset_visualizer import DysplaciaNetDatasetVisualizer

visualizer = DysplaciaNetDatasetVisualizer()
visualizer.load_dataset()
visualizer.print_dataset_summary()
visualizer.display_sample_info()
```

## 🔧 Dependencies

### Core Dependencies:
- `tensorflow==2.3.0` - Deep learning framework
- `keras==2.4.3` - High-level neural networks API
- `numpy==1.18.5` - Numerical computing (compatible version)
- `protobuf==3.20.3` - Protocol buffers (compatible version)
- `pillow` - Image processing
- `opencv-python` - Computer vision library

### Optional Dependencies:
- `matplotlib` - Plotting (may have version conflicts)
- `seaborn` - Statistical visualization (may have version conflicts)
- `pandas` - Data manipulation (may have version conflicts)

## ⚠️ Known Issues and Solutions

### Version Compatibility
- **TensorFlow 2.3.0** requires specific versions of dependencies
- **NumPy 1.18.5** is required (newer versions cause compatibility issues)
- **Protobuf 3.20.3** is required (newer versions cause descriptor errors)

### Common Errors and Solutions:

1. **"Descriptors cannot be created directly" error:**
   ```bash
   pip install protobuf==3.20.3
   ```

2. **NumPy compatibility issues:**
   ```bash
   pip install numpy==1.18.5
   ```

3. **Matplotlib import errors:**
   - The scripts work without matplotlib for core functionality
   - For visualization features, install compatible matplotlib version separately

4. **CUDA warnings:**
   - These are normal if you don't have a GPU
   - The model will run on CPU

## 📈 Model Metrics

The model analysis provides:

### Basic Metrics:
- Model architecture (Sequential CNN)
- Input/Output shapes (299x299x3 → 1)
- Parameter counts (72,977 total parameters)
- Layer information (18 layers total)

### Layer Analysis:
- 4 Conv2D layers with ReLU activation
- 4 MaxPooling2D layers for downsampling
- 1 Flatten layer
- 2 Dense layers (16 units → 1 unit)
- 1 Dropout layer for regularization

### Dataset Metrics:
- 56 total images (28 Dysplastic, 28 Normal)
- 82.14% overall accuracy
- Balanced class distribution
- Detailed error analysis

## 🤝 Contributing

To contribute to this project:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with the provided scripts
5. Submit a pull request

## 📄 License

This project is provided for educational and research purposes. Please refer to the original DysplaciaNet study for licensing information.

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Ensure all dependencies are correctly installed
3. Verify that model and data files are in the correct locations
4. Check Python version compatibility

For additional support, please create an issue in the repository.
