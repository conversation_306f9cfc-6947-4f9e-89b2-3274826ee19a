Metadata-Version: 2.1
Name: matplotlib
Version: 3.3.4
Summary: Python plotting package
Home-page: https://matplotlib.org
Author: <PERSON>, <PERSON>
Author-email: <EMAIL>
License: PSF
Download-URL: https://matplotlib.org/users/installing.html
Project-URL: Documentation, https://matplotlib.org
Project-URL: Source Code, https://github.com/matplotlib/matplotlib
Project-URL: Bug Tracker, https://github.com/matplotlib/matplotlib/issues
Project-URL: Forum, https://discourse.matplotlib.org/
Project-URL: Donate, https://numfocus.org/donate-to-matplotlib
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Matplotlib
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Education
Classifier: License :: OSI Approved :: Python Software Foundation License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Scientific/Engineering :: Visualization
Requires-Python: >=3.6
Description-Content-Type: text/x-rst
Requires-Dist: cycler (>=0.10)
Requires-Dist: kiwisolver (>=1.0.1)
Requires-Dist: numpy (>=1.15)
Requires-Dist: pillow (>=6.2.0)
Requires-Dist: pyparsing (!=2.0.4,!=2.1.2,!=2.1.6,>=2.0.3)
Requires-Dist: python-dateutil (>=2.1)

|PyPi|_ |Downloads|_ |NUMFocus|_

|DiscourseBadge|_ |Gitter|_ |GitHubIssues|_ |GitTutorial|_

|Travis|_ |AzurePipelines|_ |AppVeyor|_ |Codecov|_ |LGTM|_

.. |Travis| image:: https://travis-ci.com/matplotlib/matplotlib.svg?branch=master
.. _Travis: https://travis-ci.com/matplotlib/matplotlib

.. |AzurePipelines| image:: https://dev.azure.com/matplotlib/matplotlib/_apis/build/status/matplotlib.matplotlib?branchName=master
.. _AzurePipelines: https://dev.azure.com/matplotlib/matplotlib/_build/latest?definitionId=1&branchName=master

.. |AppVeyor| image:: https://ci.appveyor.com/api/projects/status/github/matplotlib/matplotlib?branch=master&svg=true
.. _AppVeyor: https://ci.appveyor.com/project/matplotlib/matplotlib

.. |Codecov| image:: https://codecov.io/github/matplotlib/matplotlib/badge.svg?branch=master&service=github
.. _Codecov: https://codecov.io/github/matplotlib/matplotlib?branch=master

.. |LGTM| image:: https://img.shields.io/lgtm/grade/python/g/matplotlib/matplotlib.svg?logo=lgtm&logoWidth=18
.. _LGTM: https://lgtm.com/projects/g/matplotlib/matplotlib

.. |DiscourseBadge| image:: https://img.shields.io/badge/help_forum-discourse-blue.svg
.. _DiscourseBadge: https://discourse.matplotlib.org

.. |Gitter| image:: https://badges.gitter.im/matplotlib/matplotlib.svg
.. _Gitter: https://gitter.im/matplotlib/matplotlib

.. |GitHubIssues| image:: https://img.shields.io/badge/issue_tracking-github-blue.svg
.. _GitHubIssues: https://github.com/matplotlib/matplotlib/issues

.. |GitTutorial| image:: https://img.shields.io/badge/PR-Welcome-%23FF8300.svg?
.. _GitTutorial: https://git-scm.com/book/en/v2/GitHub-Contributing-to-a-Project

.. |PyPi| image:: https://badge.fury.io/py/matplotlib.svg
.. _PyPi: https://badge.fury.io/py/matplotlib

.. |Downloads| image:: https://pepy.tech/badge/matplotlib/month
.. _Downloads: https://pepy.tech/project/matplotlib/month

.. |NUMFocus| image:: https://img.shields.io/badge/powered%20by-NumFOCUS-orange.svg?style=flat&colorA=E1523D&colorB=007D8A
.. _NUMFocus: https://numfocus.org

.. image:: https://matplotlib.org/_static/logo2.svg

Matplotlib is a comprehensive library for creating static, animated, and interactive visualizations in Python.

Check out our `home page <https://matplotlib.org/>`_ for more information.

.. image:: https://matplotlib.org/_static/readme_preview.png

Matplotlib produces publication-quality figures in a variety of hardcopy formats
and interactive environments across platforms. Matplotlib can be used in Python scripts,
the Python and IPython shell, web application servers, and various
graphical user interface toolkits.


Install
=======

For installation instructions and requirements, see `INSTALL.rst <INSTALL.rst>`_  or the
`install <https://matplotlib.org/users/installing.html>`_ documentation.

Test
====

After installation, launch the test suite::

  python -m pytest

Read the `testing guide <https://matplotlib.org/devel/testing.html>`_ for more information and alternatives.

Contribute
==========
You've discovered a bug or something else you want to change - excellent!

You've worked out a way to fix it – even better!

You want to tell us about it – best of all!

Start at the `contributing guide <https://matplotlib.org/devdocs/devel/contributing.html>`_!

Contact
=======

`Discourse <https://discourse.matplotlib.org/>`_ is the discussion forum for general questions and discussions and our recommended starting point.

Our active mailing lists (which are mirrored on Discourse) are:

* `Users <https://mail.python.org/mailman/listinfo/matplotlib-users>`_ mailing list: <EMAIL>
* `Announcement  <https://mail.python.org/mailman/listinfo/matplotlib-announce>`_ mailing list: <EMAIL>
* `Development <https://mail.python.org/mailman/listinfo/matplotlib-devel>`_ mailing list: <EMAIL>

Gitter_ is for coordinating development and asking questions directly related
to contributing to matplotlib.


Citing Matplotlib
=================
If Matplotlib contributes to a project that leads to publication, please
acknowledge this by citing Matplotlib.

`A ready-made citation entry <https://matplotlib.org/citing.html>`_ is available.


