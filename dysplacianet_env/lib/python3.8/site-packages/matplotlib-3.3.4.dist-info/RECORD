__pycache__/pylab.cpython-38.pyc,,
matplotlib-3.3.4-py3.8-nspkg.pth,sha256=FgO_3ug071EXEKT8mgOPBUhyrswPtPCYjOpUCyau7UU,569
matplotlib-3.3.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.3.4.dist-info/LICENSE,sha256=WhqB6jAXKMi7opM9qDLAzWIina8giToCSrPVMkRGjbw,4830
matplotlib-3.3.4.dist-info/LICENSE_AMSFONTS,sha256=FVFB1Zh38zj24cCAXem3mWTc5x_l0qVsROOLLA9-Ne4,12675
matplotlib-3.3.4.dist-info/LICENSE_BAKOMA,sha256=WIfu5aAEHJn_BrjwP0Tc1zA8C_-NxwhOie4y32RY50s,1440
matplotlib-3.3.4.dist-info/LICENSE_CARLOGO,sha256=YZAtXu803SSHC3KHqWJg0zKCM7lvcgK_cK1uKg2i3j8,4455
matplotlib-3.3.4.dist-info/LICENSE_COLORBREWER,sha256=7FIbyIlwg2PD2R0pDZCClCN3gRfqJZABk-mOKfUiJAg,1968
matplotlib-3.3.4.dist-info/LICENSE_JSXTOOLS_RESIZE_OBSERVER,sha256=WXdWrctR8kPvT7OGkgN39h0BKs4JBDZOGo7pquxq_IQ,6799
matplotlib-3.3.4.dist-info/LICENSE_QT4_EDITOR,sha256=srUMqLYXKsojCVrfFduJ03J-nvLW7wF45CcjQBG-080,1230
matplotlib-3.3.4.dist-info/LICENSE_SOLARIZED,sha256=EtUyf7xN-EWoaIPeme1f30GYRF1W26zfX62PDv3JdRM,1121
matplotlib-3.3.4.dist-info/LICENSE_STIX,sha256=TMPvujo6YE62-TchHkbaHiFIgwBWpuCbzBnfQXDSUqQ,3914
matplotlib-3.3.4.dist-info/LICENSE_YORICK,sha256=yrdT04wJNlHo3rWrtoTj7WgCDg5BgDT5TXnokNx66E0,2313
matplotlib-3.3.4.dist-info/METADATA,sha256=DlbbIgbtoERnnZnJ9CSvWgLXVeZJ1NuBO73QfHgd35U,5662
matplotlib-3.3.4.dist-info/RECORD,,
matplotlib-3.3.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.3.4.dist-info/WHEEL,sha256=lmsU4pfb_60LHFgAOwGQv63gTX7Ny9agYdFe9vUdh58,108
matplotlib-3.3.4.dist-info/namespace_packages.txt,sha256=A2PHFg9NKYOU4pEQ1h97U0Qd-rB-65W34XqC-56ZN9g,13
matplotlib-3.3.4.dist-info/top_level.txt,sha256=9tEw2ni8DdgX8CceoYHqSH1s50vrJ9SDfgtLIG8e3Y4,30
matplotlib/__init__.py,sha256=sWC0DXgllHFP4RuZUOMPUtEtZ-YBYiQyRFOOJjsNNDE,50742
matplotlib/__pycache__/__init__.cpython-38.pyc,,
matplotlib/__pycache__/_animation_data.cpython-38.pyc,,
matplotlib/__pycache__/_cm.cpython-38.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-38.pyc,,
matplotlib/__pycache__/_color_data.cpython-38.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-38.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-38.pyc,,
matplotlib/__pycache__/_layoutbox.cpython-38.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-38.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-38.pyc,,
matplotlib/__pycache__/_text_layout.cpython-38.pyc,,
matplotlib/__pycache__/_version.cpython-38.pyc,,
matplotlib/__pycache__/afm.cpython-38.pyc,,
matplotlib/__pycache__/animation.cpython-38.pyc,,
matplotlib/__pycache__/artist.cpython-38.pyc,,
matplotlib/__pycache__/axis.cpython-38.pyc,,
matplotlib/__pycache__/backend_bases.cpython-38.pyc,,
matplotlib/__pycache__/backend_managers.cpython-38.pyc,,
matplotlib/__pycache__/backend_tools.cpython-38.pyc,,
matplotlib/__pycache__/bezier.cpython-38.pyc,,
matplotlib/__pycache__/blocking_input.cpython-38.pyc,,
matplotlib/__pycache__/category.cpython-38.pyc,,
matplotlib/__pycache__/cm.cpython-38.pyc,,
matplotlib/__pycache__/collections.cpython-38.pyc,,
matplotlib/__pycache__/colorbar.cpython-38.pyc,,
matplotlib/__pycache__/colors.cpython-38.pyc,,
matplotlib/__pycache__/container.cpython-38.pyc,,
matplotlib/__pycache__/contour.cpython-38.pyc,,
matplotlib/__pycache__/dates.cpython-38.pyc,,
matplotlib/__pycache__/docstring.cpython-38.pyc,,
matplotlib/__pycache__/dviread.cpython-38.pyc,,
matplotlib/__pycache__/figure.cpython-38.pyc,,
matplotlib/__pycache__/font_manager.cpython-38.pyc,,
matplotlib/__pycache__/fontconfig_pattern.cpython-38.pyc,,
matplotlib/__pycache__/gridspec.cpython-38.pyc,,
matplotlib/__pycache__/hatch.cpython-38.pyc,,
matplotlib/__pycache__/image.cpython-38.pyc,,
matplotlib/__pycache__/legend.cpython-38.pyc,,
matplotlib/__pycache__/legend_handler.cpython-38.pyc,,
matplotlib/__pycache__/lines.cpython-38.pyc,,
matplotlib/__pycache__/markers.cpython-38.pyc,,
matplotlib/__pycache__/mathtext.cpython-38.pyc,,
matplotlib/__pycache__/mlab.cpython-38.pyc,,
matplotlib/__pycache__/offsetbox.cpython-38.pyc,,
matplotlib/__pycache__/patches.cpython-38.pyc,,
matplotlib/__pycache__/path.cpython-38.pyc,,
matplotlib/__pycache__/patheffects.cpython-38.pyc,,
matplotlib/__pycache__/pylab.cpython-38.pyc,,
matplotlib/__pycache__/pyplot.cpython-38.pyc,,
matplotlib/__pycache__/quiver.cpython-38.pyc,,
matplotlib/__pycache__/rcsetup.cpython-38.pyc,,
matplotlib/__pycache__/sankey.cpython-38.pyc,,
matplotlib/__pycache__/scale.cpython-38.pyc,,
matplotlib/__pycache__/spines.cpython-38.pyc,,
matplotlib/__pycache__/stackplot.cpython-38.pyc,,
matplotlib/__pycache__/streamplot.cpython-38.pyc,,
matplotlib/__pycache__/table.cpython-38.pyc,,
matplotlib/__pycache__/texmanager.cpython-38.pyc,,
matplotlib/__pycache__/text.cpython-38.pyc,,
matplotlib/__pycache__/textpath.cpython-38.pyc,,
matplotlib/__pycache__/ticker.cpython-38.pyc,,
matplotlib/__pycache__/tight_bbox.cpython-38.pyc,,
matplotlib/__pycache__/tight_layout.cpython-38.pyc,,
matplotlib/__pycache__/transforms.cpython-38.pyc,,
matplotlib/__pycache__/ttconv.cpython-38.pyc,,
matplotlib/__pycache__/type1font.cpython-38.pyc,,
matplotlib/__pycache__/units.cpython-38.pyc,,
matplotlib/__pycache__/widgets.cpython-38.pyc,,
matplotlib/_animation_data.py,sha256=-u1mENRfiYh4Mk4sQsgBOF09V20sovW2AKDu5spraJ0,7729
matplotlib/_cm.py,sha256=JNugoAjPt58HTVgKbTocqYmun2NAs3vZ1wAvsY4YlR0,66566
matplotlib/_cm_listed.py,sha256=hpgMx7bjxJx5nl1PbQvaCDUBHQf8njaRrM2iMaBeZOM,109462
matplotlib/_color_data.py,sha256=K2HSKblmuh-X_1ZZ9TcXcP7iKHaGC4mC_ScWqX_tdXE,34947
matplotlib/_constrained_layout.py,sha256=-S_stIE0XVpnvS6pcJLfvUq0XrRZzpfi4vDo_irGX_o,27418
matplotlib/_contour.cpython-38-x86_64-linux-gnu.so,sha256=rx_itUxsgnixrAgDHd1dEq9cnROKf4TO7FzRWf1_Z8U,695697
matplotlib/_image.cpython-38-x86_64-linux-gnu.so,sha256=oY8MSRo8GesXLQb7arYEuGJbjtt4EhH1d0fupCUXtug,2528739
matplotlib/_internal_utils.py,sha256=nhK6LLWYW93fBcsFiO09JmqFj2rgHEsGYFOeaC7HRKw,2140
matplotlib/_layoutbox.py,sha256=qwyq7ju6ok_K1Z_MKMV-TwoiEMc-FYGxHObpw99raK4,23634
matplotlib/_mathtext_data.py,sha256=_EwmYih22UppDlyQ7zhltVu8hpnMS9x6iiMTXVTR3BE,56423
matplotlib/_path.cpython-38-x86_64-linux-gnu.so,sha256=DLYyWX6qHxs14X19aLcBL8f3X8-MnZZVW1v5iPD4T8M,1710022
matplotlib/_pylab_helpers.py,sha256=mWAoMqEHRaZr44B0wM1sKjg_oCGrUPHWYIpBmgvXGRA,4500
matplotlib/_qhull.cpython-38-x86_64-linux-gnu.so,sha256=VnOp_l3FvNXCVZdMb9YfrEV9vIF0eyNkmP_98QxWSF8,1508280
matplotlib/_text_layout.py,sha256=88DxzfAOPzpRjpu0OwLaRl6eOVJ5Var8ZxrDyhAQ7C8,1036
matplotlib/_tri.cpython-38-x86_64-linux-gnu.so,sha256=Phvd1z-UY6e34dZLInhbqATk_HhQq_qLtEkn9sHl11I,1304959
matplotlib/_ttconv.cpython-38-x86_64-linux-gnu.so,sha256=Inf2hKsjNsrYtlphAAVkYt2vLpDfFgayo_A6ywunIwQ,451654
matplotlib/_version.py,sha256=SqCMOp_lSVZOGccgapc7ebC7euTN1fjnXXz_hJqqpiE,471
matplotlib/afm.py,sha256=y5a3lMwOlb_GUe5BEYGoBBlCRcQt07Cg2SmqsZI6swc,16577
matplotlib/animation.py,sha256=aOw6Zk-iRh4suTMTkzT4xDSCpEglgG2Z2tL14A_MEVI,67994
matplotlib/artist.py,sha256=eeB7KTb3od6huB9uF3xk495GlJU9lsVQd0Kyniu5HH0,54000
matplotlib/axes/__init__.py,sha256=npQuBvs_xEBEGUP2-BBZzCrelsAQYgB1U96kSZTSWIs,46
matplotlib/axes/__pycache__/__init__.cpython-38.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-38.pyc,,
matplotlib/axes/__pycache__/_base.cpython-38.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-38.pyc,,
matplotlib/axes/__pycache__/_subplots.cpython-38.pyc,,
matplotlib/axes/_axes.py,sha256=wNxl4G4phJb5FtBuURkfLkKdlfrrr4E7rEjf_JzpTHg,313063
matplotlib/axes/_base.py,sha256=QrB04bGPZ8ObLKSyqZLobxUZ-X09lnU7xY_08Zh_e-k,159042
matplotlib/axes/_secondary_axes.py,sha256=-7f4ovuJ9Mj6tA_01wPxsMD13Z_0HPIT-T7TLKkeNog,13360
matplotlib/axes/_subplots.py,sha256=aBxf2fNRl5NNFeo8iURzMLTZwmYc0xY1Uhs4XJWWBtw,9658
matplotlib/axis.py,sha256=fjX055IvBnHFRaAXLfqL-Nw8N2SKRJe0omaV6UfqcEU,92664
matplotlib/backend_bases.py,sha256=nO4ZCPnQ5wMNAv9iVa2Y_W_ZiiAbN5_kjQSLjA8OiFg,127296
matplotlib/backend_managers.py,sha256=UPyhQ9Q8a_H_FPwFWkctjghrVkCp-ZE4xxaav4RlW74,13859
matplotlib/backend_tools.py,sha256=kYQD9ouoD6sHV2tQzPwVhUKDW1OrM_zRoMwydHms8AA,34935
matplotlib/backends/__init__.py,sha256=X-F5YPMYcPpXRDsjWBLeffSWcVYsdcW4-qJCfTZlUNQ,107
matplotlib/backends/__pycache__/__init__.cpython-38.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-38.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt4.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt4agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt4cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-38.pyc,,
matplotlib/backends/_backend_agg.cpython-38-x86_64-linux-gnu.so,sha256=U2wZF5udJ7QNFUU0j4CmguYn8VI3lmsI0j0k5dOM5fk,3841494
matplotlib/backends/_backend_pdf_ps.py,sha256=hVNIb4OiNqUcu01MwPzfTmyAwi3fyt-HNKyO1L8e1T8,3774
matplotlib/backends/_backend_tk.py,sha256=NDRsSEfimDxGiOUYZh2Erga2g630lIhGo80OcmItPqU,32859
matplotlib/backends/_tkagg.cpython-38-x86_64-linux-gnu.so,sha256=6XPkFEnrL4pKLNxIPKOHDAjiOmUuJ35a0MCYfM_F-E4,33837
matplotlib/backends/backend_agg.py,sha256=8DHxKReLBNd0HyCOn9NnEUhCJPKP5uqkoYPVzyVJAjE,23006
matplotlib/backends/backend_cairo.py,sha256=Ot7ArUPUXEa29tEYhCqft5hV7L7CYe0POx6j7Onyet8,18680
matplotlib/backends/backend_gtk3.py,sha256=L1OTDahZGGyGmO3moMg1ome9fMPqX0DEzkJY9VGD1m8,33094
matplotlib/backends/backend_gtk3agg.py,sha256=h6IVIBHR2zDy0SNm-N9PxTcddZnJSJ7mmayKemLDgvc,2804
matplotlib/backends/backend_gtk3cairo.py,sha256=7q60kRBN8LPAWljbRxIdw8W146jF1jv8BtTmX8TPYpQ,1351
matplotlib/backends/backend_macosx.py,sha256=eSuOMg8XZ_2KjuyeIB3oRMqRBGZmHx2-uoIbRJeIMAs,5678
matplotlib/backends/backend_mixed.py,sha256=tkALWz3fuHPiFM89GkkCXdGWQs0PFR-lujwHDyy236c,5190
matplotlib/backends/backend_nbagg.py,sha256=rh5SDP7D9sR_PWU2MHvg9W5zLvsGVb8snmwPZLOl_5A,8551
matplotlib/backends/backend_pdf.py,sha256=izNzt-IiOPAkOMgjmxPC9Bwi6V7f98mUzV5KCkfUYs8,97014
matplotlib/backends/backend_pgf.py,sha256=PKhB7m-xfzUnd95znqKhNtOQLKooRfpW2O4e2A451X0,42998
matplotlib/backends/backend_ps.py,sha256=L6K5pskWcjxb5KSqsEmYYDOy6SIVtXYvlihuHvFPNns,46731
matplotlib/backends/backend_qt4.py,sha256=5pWOrGgQXYBE_ohTWaju7ZwYFCvDzGatgjHTIYvvd84,513
matplotlib/backends/backend_qt4agg.py,sha256=6vsXxzUBN4tM3zuuY6RRtioruicJwp_HsSd4yEOFu6o,379
matplotlib/backends/backend_qt4cairo.py,sha256=Md2XqfttMCsx1dZfCX7zEFiGEXN107wEt_fxKNtya44,316
matplotlib/backends/backend_qt5.py,sha256=RuoVCpuLo0dMkfpT1w033fQCjmr3kRIsQfaHe7s7cNA,39226
matplotlib/backends/backend_qt5agg.py,sha256=T0Lgc8J2tCkb3RU_blQpSOhX_qBB5zkdeEQ81NbtPe8,3160
matplotlib/backends/backend_qt5cairo.py,sha256=H3BSQNQD4GxzA6zeQ05M2h0Semd5zJIxzhvK6fzVVPg,1819
matplotlib/backends/backend_svg.py,sha256=qTW5u5uAjMHTk3ufYVGQtrs2qlgeaptUdHU-DVIaLg8,49161
matplotlib/backends/backend_template.py,sha256=7-lDt_AALk33KQ4pbiuzmKol2scb0Py43sDDK6s3Dak,8442
matplotlib/backends/backend_tkagg.py,sha256=WMslLWYmtxlmAaBH4tx4HjmRDWMKiSV91KHF9yeMRng,676
matplotlib/backends/backend_tkcairo.py,sha256=dVCh7ZD_2OR0DBQ0N3icD8cDV1SeEzCsRja446wWhPw,1069
matplotlib/backends/backend_webagg.py,sha256=Y1jvjX7-ZTi-rr_B4FicNAQa6xhb5ebAc9AcBveXaNU,11055
matplotlib/backends/backend_webagg_core.py,sha256=n6E3Gjs2yXkPuDf8IFS_sdYXG2GDj6lHUlno-OinMZ8,18033
matplotlib/backends/backend_wx.py,sha256=KMpuAwDhO4LYFYkuulNpubWUaxl7BQQ9ST1MvgpfVGQ,60627
matplotlib/backends/backend_wxagg.py,sha256=fsRREmFmyOuTCrSQMEYsrCnJ4me00_c8JNbHk2gTrRU,2932
matplotlib/backends/backend_wxcairo.py,sha256=-K5wJxpggKlWn7vnJW5pCo5t2dIiapbMyNMX_U6jd_k,1833
matplotlib/backends/qt_compat.py,sha256=2aCYWcx7R62zXCZ95sb4ONSrLtJklz9StrJeIdVGxMw,8132
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-38.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-38.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formsubplottool.cpython-38.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-38.pyc,,
matplotlib/backends/qt_editor/__pycache__/formsubplottool.cpython-38.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=Ptk7uWeDnMAK_RI6VsaE9jGuPhjp507I4mSm-DEgZFo,20581
matplotlib/backends/qt_editor/_formsubplottool.py,sha256=pqnL7mEdfvYoh-QLpww3HBwKGmRtYtc2xVpB0elL-Pg,1507
matplotlib/backends/qt_editor/figureoptions.py,sha256=XRjakdBeSxnUJwQDHYc1xGyRzfuOAW8rVJCC5ro3y_o,9471
matplotlib/backends/qt_editor/formsubplottool.py,sha256=S_v3MgumItnQxb06TSItN5HsGPLhHHeNhbAajrgYRVY,236
matplotlib/backends/web_backend/.eslintrc.js,sha256=pZxDrJU80urQlDMoO6A3ylTeZ7DgFoexDhi93Yfm6DU,666
matplotlib/backends/web_backend/.prettierignore,sha256=L47QXaDPUyI-rMmNAmn-OQH-5-Gi04-ZGl7QXdjP7h8,97
matplotlib/backends/web_backend/.prettierrc,sha256=OjC7XB1lRdhntVFThQG-J-wRiqwY1fStHF2i0XTOrbk,145
matplotlib/backends/web_backend/all_figures.html,sha256=K0MyVxwnpQuJl-PPvsrCALlCuyjbgKo_dXum72_YmoA,1620
matplotlib/backends/web_backend/css/boilerplate.css,sha256=qui16QXRnQFNJDbcMasfH6KtN9hLjv8883U9cJmsVCE,2310
matplotlib/backends/web_backend/css/fbm.css,sha256=Us0osu_rK8EUAdp_GXrh89tN_hUNCN-r7N1T1NvmmwI,1473
matplotlib/backends/web_backend/css/mpl.css,sha256=ruca_aA5kNnP-MZmLkriu8teVP1nIgwcFEpoB16j8Z4,1611
matplotlib/backends/web_backend/css/page.css,sha256=JJqTPCwFeUy3AgHJTv_wbX00AQd-4qVUYCSrcTztluo,1624
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=yjJa-Vjwk58BKNVlsP2bugLhednhdnCBB88kvP1nqmM,1311
matplotlib/backends/web_backend/js/mpl.js,sha256=HWx-MxXvX2DnSQIjJhNX3dgwgFl89IHLvuNKewW0j9o,23332
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=Zs2Uzs7YUilG765nYvanCo-IK8HkHDtIum1KAq6bQ_w,302
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=8xVM74nHAWaP82qGi_kk7xnpanENkXl-Nr9d97YKtZM,8961
matplotlib/backends/web_backend/nbagg_uat.ipynb,sha256=y1N8hQzBJ05rJ2hZla2_Mw6tOUfNP1UHKo636W1e098,15933
matplotlib/backends/web_backend/package.json,sha256=vl3nGaoaVKYGnxlhZP7kVyAqoNmbQbKmEBnQwUWDmwE,545
matplotlib/backends/web_backend/single_figure.html,sha256=COFDb5xbaFoVrY5o0SmDWv5bWb9HkZR2pGRZx4hzCow,1239
matplotlib/bezier.py,sha256=sgcrKRi7wgA6N5C-PwKFiIrSswityHq_TschvXB5X04,19476
matplotlib/blocking_input.py,sha256=qXeHCx72m5isp08xNIzUtpGhVtq9J5sAtKfONonk0ZI,11301
matplotlib/category.py,sha256=W8xq1Kh4yVzMHQWh3la78DXL3Ya1yy0c0dRr9aFHGn4,7164
matplotlib/cbook/__init__.py,sha256=9ZrFn8daVVVLiqRrOANqr_g2VY6FWmnLkPLhInLC5U0,77217
matplotlib/cbook/__pycache__/__init__.cpython-38.pyc,,
matplotlib/cbook/__pycache__/deprecation.cpython-38.pyc,,
matplotlib/cbook/deprecation.py,sha256=n7HAp2ynRCVGyVgP4g6faZAbrRl1irMlAmKy9ul5XHE,18912
matplotlib/cm.py,sha256=W0ZNghptxpzv1MQ7cfsA0J4cpWxFC0EJcUkOaK-XnN4,16743
matplotlib/collections.py,sha256=LqHHAsMShjGuqdUPX5d-8YlMd0O7woaneE-_Mdu3zWc,77012
matplotlib/colorbar.py,sha256=pgUltq3zeqiskVjb5kyDw_X4EMolnxlljuKiEYuSv7Y,65766
matplotlib/colors.py,sha256=zUiIcEI1beoLMpurfARiOld7yrSYy_eDzYjK8b8V54g,78296
matplotlib/compat/__init__.py,sha256=oto0cT2n56P0X0nWDRkGfYj-lsrKlwoFiQNG6ZWi9e0,94
matplotlib/compat/__pycache__/__init__.cpython-38.pyc,,
matplotlib/container.py,sha256=l_-yWxnuI0bk2xczyyzunKsna8T7BCxOObjonqLXS0g,4305
matplotlib/contour.py,sha256=Oo42M57NoZPtyQXY8OHeNa0sScSTBjuLNelRwikZA1Y,69101
matplotlib/dates.py,sha256=wX8dTCv6TeadQ0REb_9115AY1UjbQuGpvdY5zR4-btU,66901
matplotlib/docstring.py,sha256=yLCe0cXdYA1Fy6JPSXVHtSpabPjNHtJ3307FqRICNNw,2436
matplotlib/dviread.py,sha256=aL1biMgTIPAn6bEheHGzgC24rOPidGpEILBLlrXu2CU,40232
matplotlib/figure.py,sha256=yikI-g88aoXBnvt8njnhuWNgwwB1sDVGb0hZZKZViZg,105934
matplotlib/font_manager.py,sha256=tVO1nIYeabQE4HuE58NOOsin6AIJ2rr0NYbl7uEMHS0,48786
matplotlib/fontconfig_pattern.py,sha256=KAW895GkX4BlctRfb8Vp3bLCO_h4_ChDRjeGqFlAhHg,6651
matplotlib/ft2font.cpython-38-x86_64-linux-gnu.so,sha256=lLUCDM7Eh650JVp297z_s8i0uuC6n74toajHWgs3r3c,1494918
matplotlib/gridspec.py,sha256=qzgFPb6hOkBCM1HP_cUy4sxmzHlgeRkOtAmdEiVvN3k,33054
matplotlib/hatch.py,sha256=vcaOXM0nxNkZxZ_rsLuNRmefcSDXm4-_9zFILNn66ps,6888
matplotlib/image.py,sha256=dcHk1GJoDx1mdTh6pgG0yY7ItQ-UXQoa8uJ6n4OjDq0,66954
matplotlib/legend.py,sha256=mBUcau_vF6iQV2Dte5yd25WYKt3Wc_3zA9NO3Iqa23g,47617
matplotlib/legend_handler.py,sha256=RA_wy27ZHKlXBhSFX3YIXnyewMuTctj3tsf9qPg6_rs,26211
matplotlib/lines.py,sha256=F_OFGitR7HoYDIHQotrPdqSgjMl2T76FFQioZvBO4EI,51549
matplotlib/markers.py,sha256=ujsSAJR-rM6O5HfSaEjBJuYkjA7Sbm7keoxohRjCV-4,31988
matplotlib/mathtext.py,sha256=8LMlPbNZDSO3jHTqs_bh75mt-PqN9ObyRPbc1yuywq4,120227
matplotlib/mlab.py,sha256=68kp1sHGqZws9ItPVJXkf6HqWN8W1v9CP1cCqwW5p0s,35666
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=blR3ERmrVBV5XKkAnDCj4NMeYVgzH7cXtJ3u59u9GuE,12070
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=5qwEOpedEo76bDUahyuuF1q0cD84tRrX-VQ4p3MlfBo,10416
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=WDvgC_D3UkGJg9u-J0U6RaT02lF4oz3lQxHtg1r3lYw,10101
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=AbmzvCVWBceHRfmRfeJ9E6xzOQTFLk0U1zDfpf3_MaM,8295
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=4ji7_mTpeWMa93o_UHBWPKCnqsBfhJJNllat1lJArP4,6501
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=jjFrigwkTpYLqa26cpzZvKQNBo-PuF4bmDVqaM4pMWw,17183
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=sgNQdeYyx8J-itGw9h31y95aMBiTCRvmNSPTXwwS7xg,17255
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=ZUtfHPloNqcvGMHMxaKDSlshhOcjwheUx143RwpGdIU,17241
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=Yj1wBg6Jsqqz1KBfhRoJ3ACR-CMQol8Fj_ZM5NZ1gDk,17346
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=Zl5o6J_di9Y5j2EpHtjew-_sfg7-WoeVmO9PzOYSTUc,15157
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=JAOno930iTyfZILMf11vWtiaTgrJcPpP6FRTRhEMMD4,15278
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=UJqJjOJ6xQDgDBLX157mKpohIJFVmHM-N6x2-DiGv14,15000
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=AWislZ2hDbs0ox_qOWREugsbS8_8lpL48LPMR40qpi0,15181
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=6j1TS2Uc7DWSc-8l42TGDc1u0Fg8JspeWfxFayjUwi8,15352
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=smg3mjl9QaBDtQIt06ko5GvaxLsO9QtTvYANuE5hfG0,15422
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=7nxFr0Ehz4E5KG_zSE5SZOhxRH8MyfnCbw-7x5wu7tw,15339
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=NKEz7XtdFkh9cA8MvY-S3UOZlV2Y_J3tMEWFFxj7QSg,15443
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=NAx4M4HjL7vANCJbc-tk04Vkol-T0oaXeQ3T2h-XUvM,17155
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=8e_myD-AQkNF7q9XNLb2m76_lX2TUr3a5wog_LIE1sk,17086
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=8fkBRmJ-SWY2YrBg8fFyjJyrJp8daQ6JPO6LvhM8xPI,17230
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=aeVRvV4r15BBvxuRJ0MG8ZHuH2HViuIiCYkvuapmkmM,17195
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=IyMYM-bgl-gI6rG0EuZZ2OLzlxJfGeSh8xqsh0t-eJQ,15627
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=s12C-eNnIDHJ_UVbuiprjxBjCiHIbS3Y8ORTC-qTpuI,15729
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=Kt8KaRidts89EBIK29X2JomDUEDxvroeaJz_RNTi6r4,17839
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=lL5fAHTRwODl-sB5mH7IfsD1tnnea4yRUK-_Ca2bQHM,17781
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=3KqK3eejiR4hIFBUynuSX_4lMdE2V2T58xOF8lX-fwc,17919
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Vx9rRf3YfasMY7tz-njSxz67xHKk-fNkN7yBi0X2IP0,17877
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=aoXepTcDQtQa_mspflMJkEFKefzXHoyjz6ioJVI0YNc,16028
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=pCWW1MYgy0EmvwaYsaYJaAI_LfrsKmDANHu7Pk0RaiU,17496
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=0CIB2BLe9r-6_Wl5ObRTTf98UOrezmGQ8ZOuBX5kLks,16665
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=5R-pLZOnaHNG8pjV6MP3Ai-d2OTQYR_cYCb5zQhzfSU,16920
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=3EzUbNnXr5Ft5eFLY00W9oWu59rHORgDXUuJaOoKN58,15662
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=X_9tVspvrcMer3OS8qvdwjFFqpAXYZneyCL2NHA902g,15810
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=ijMb497FDJ9nVdVMb21F7W3-cu9sb_9nF0oriFpSn8k,15752
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=8KITbarcUUMi_hdoRLLmNHtlqs0TtOSKqtPFft7X5nY,15733
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=Iyt8ajE4B2Tm34oBj2pKtctIf9kPfq05suQefq8p3Ro,9644
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=bL1fA1NC4_nW14Zrnxz4nHlXJb4dzELJPvodqKnYeMg,17983
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=-_Ui6XlKaFTHEnkoS_-1GtIr5VtGa3gFQ2ezLOYHs08,18070
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=IEcsWcmzJyjCwkgsw4o6hIMmzlyXUglJat9s1PZNnEU,17942
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=49fQMg5fIGguZ7rgc_2styMK55Pv5bPTs7wCzqpcGpk,18068
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=qMaHTdpkrNL-m4DWhjpxJCSmgYkCv1qIzLlFfM0rl40,21532
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=g7AVJyiTxeMpNk_1cSfmYgM09uNUfPlZyWGv3D1vcAk,21931
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=XYmNC5GQgSVAZKTIYdYeNksE6znNm9GF_0SmQlriqx0,22148
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=i7fVe-iLyLtQxCfAa4IxdxH-ufcHmMk7hbCGG5TxAY4,21891
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=wyuoIWEZOcoXrSl1tPzLkEahik7kGi91JJj-tkFRG4A,16250
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=MyjLAnzKYRdQBfof1W3k_hf30MvqOkqL__G22mQ5xww,9467
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=sIDDI-B82VZ3C0mI_mHFITCZ7PVn37AIYMv1CrHX4sE,15333
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=zg61QobD3YU9UBfCXmvmhBNaFKno-xj8sY0b2RpgfLw,15399
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=vRQm5j1sTUN4hicT1PcVZ9P9DTTUHhEzfPXqUUzVZhE,15441
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Mdcq2teZEBJrIqVXnsnhee7oZnTs6-P8_292kWGTrw4,15335
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=i2l4gcjuYXoXf28uK7yIVwuf0rnw6J7PwPVQeHj5iPw,69269
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=Um5O6qK11DXLt8uj_0IoWkc84TKqHK3bObSKUswQqvY,69365
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=hVYDg2b52kqtbVeCzmiv25bW1yYdpkZS-LXlGREN2Rs,74392
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=23cvKDD7bQAJB3kdjSahJSTZaUOppznlIO6FXGslyW8,74292
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=P5UaoXr4y0qh4SiMa5uqijDT6ZDr2-jPmj1ayry593E,9740
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=cQTmr2LFPwKQE_sGQageMcmFicjye16mKJslsJLHQyE,64251
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=pzWOdycm6RqocBWgAVY5Jq0z3Fp7LuqWgLNMx4q6OFw,59642
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=bK5puSMpGT_YUILwyJrXoxjfj7XJOdfv5TQ_iKsJRzw,66328
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=hhNrUnpazuDDKD1WpraPxqPWCYLrO7D7bMVOg-zI13o,60460
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=ZuOmt9GcKofjdOq8kqhPhtAIhOwkL2rTJTmZxAjFakA,9527
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=MRv8ppSITYYAb7lt5EOw9DWWNZIblfxsFhu5TQE7cpI,828
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=11k43sCY8G8Kw8AIUwZdlPAgvhw8Yu8dwpdboVtNmw4,4816
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=cxFOZdp1AxNhXR6XxCzf5iJpNcu-APm-geOHhD-s0h8,5475
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back.gif,sha256=sdkxFRAh-Mgs44DTvruO5OxcI3Av9CS1g5MqMA_DDkQ,608
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back_large.gif,sha256=tqCtecrxNrPuDCUj7FGs8UXWftljKcwgp5cSBBhXwiQ,799
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave.gif,sha256=wAyNwOPd9c-EIPwcUAlqHSfLmxq167nhDVppOWPy9UA,723
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave_large.gif,sha256=IXrenlwu3wwO8WTRvxHt_q62NF6ZWyqk3jZhm6GE-G8,1498
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward.gif,sha256=VNL9R-dECOX7wUAYPtU_DWn5hwi3SwLR17DhmBvUIxE,590
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward_large.gif,sha256=H6Jbcc7qJwHJAE294YqI5Bm-5irofX40cKRvYdrG_Ig,786
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.gif,sha256=3lRfmAqQU7A2t1YXXsB9IbwzK7FaRh-IZO84D5-xCrw,1267
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=tsVIES_nINrAbH4FqdsCGOx0SVE37vcofSYBhnnaOP0,4888
matplotlib/mpl-data/images/hand_large.gif,sha256=H5IHmVTvOqHQb9FZ_7g7AlPt9gv-zRq0L5_Q9B7OuvU,973
matplotlib/mpl-data/images/help-symbolic.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help.gif,sha256=3Cjr7YqfH7HFmYCmrJKxnoLPkbUfUcxQOW7RI2-4Cpo,564
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.ppm,sha256=mVPvgwcddzCM-nGZd8Lnl_CorzDkRIXQE17b7qo8vlU,1741
matplotlib/mpl-data/images/help.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/help_large.ppm,sha256=MiCSKp1Su88FXOi9MTtkQDA2srwbX3w5navi6cneAi4,6925
matplotlib/mpl-data/images/home-symbolic.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home.gif,sha256=NKuFM7tTtFngdfsOpJ4AxYTL8PYS5GWKAoiJjBMwLlU,666
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home_large.gif,sha256=k86PJCgED46sCFkOlUYHA0s5U7OjRsc517bpAtU2JSw,1422
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_128.ppm,sha256=IHPRWXpLFRq3Vb7UjiCkFrN_N86lSPcfrEGunST08d8,49167
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move.gif,sha256=FN52MptH4FZiwmV2rQgYCO2FvO3m5LtqYv8jk6Xbeyk,679
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move_large.gif,sha256=RMIAr-G9OOY7vWC04oN6qv5TAHJxhQGhLsw_bNsvWbg,951
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=E00YoX7u4NrxMHm_L1TM8PDJ88bX5qRdCrO-Uj59CEA,1244
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots.gif,sha256=QfhmUdcrko08-WtrzCJUjrVFDTvUZCJEXpARNtzEwkg,691
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots_large.gif,sha256=Ff3ERmtVAaGP9i1QGUNnIIKac6LGuSW2Qf4DrockZSI,1350
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect.gif,sha256=mTX6h9fh2W9zmvUYqeibK0TZ7qIMKOB1nAXMpD_jDys,696
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect_large.gif,sha256=nx5LUpTAH6ZynM3ZfZDS-wR87jbMUsUnyQ27NGkV0_c,1456
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/matplotlibrc,sha256=QwmkRCCHLTbVgwKy5myHxqjjILbwI1WGg5DgePCFA5E,39901
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/None_vs_nearest-pdf.png,sha256=5CPvcG3SDNfOXx39CMKHCNS9JKZ-fmOUwIfpppNXsQ0,106228
matplotlib/mpl-data/sample_data/README.txt,sha256=ABz19VBKfGewdY39QInG9Qccgn1MTYV3bT5Ph7TCy2Y,128
matplotlib/mpl-data/sample_data/aapl.npz,sha256=GssVYka_EccteiXbNRJJ5GsuqU7G8F597qX7srYXZsw,107503
matplotlib/mpl-data/sample_data/ada.png,sha256=X1hjJK1_1Nc8DN-EEhey3G7Sq8jBwQDKNSl4cCAE0uY,308313
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/ct.raw.gz,sha256=LDvvgH-mycRQF2D29-w5MW94ZI0opvwKUoFI8euNpMk,256159
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=A0SU3buOUGhT-NI_6LQ6p70fFSIU3iLFdgzvzrKR6SE,132
matplotlib/mpl-data/sample_data/demodata.csv,sha256=MRybziqnyrqMCH9qG7Mr6BwcohIhftVG5dejXV2AX2M,659
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=cUqVw5vDHNSZoaO4J0ebZUf5SrJP36775abs7R9Bclg,2186
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/grace_hopper.png,sha256=MCf0ju2kpC40srQ0xw4HEyOoKhLL4khP3jHfU9_dR7s,628280
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=ITxkJUsan2oqXgJDy6DJvwJ4aHviKeWGnxPkTjXUt7A,33541
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=GArKb0O3DgKZRsKdJf6lX3rMSf-PCekIiBoLNdgF7Mk,3211
matplotlib/mpl-data/sample_data/percent_bachelors_degrees_women_usa.csv,sha256=TzoqamsV_N3d3lW7SKmj14zZVX4FOOg9jJcsC5U9pbA,5681
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=PECeO60wwJe2sSDvxapBJRuKGek0qLcoaN8qOX6tgNQ,1255
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=iopHpMaM3im_AK2aiHGuM2DKM5i9Kc84v6NQEoSb10Q,167
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=-KbhaI859BITHIoyUZIfpQDjfckgLAlDAS_ydKsm6mc,712
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=GW1PkcxZ0PwnU3bCF4p4v4yGrGySRiMBSuvVzyuuL8Y,24228
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=Ht6phZUy3zNRdcfHKcSb1uh3O8DunSPX8HPt9xTyzuo,658
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=yTa2YEIIP9xi5V_G0p2vSlxghuhNwjRi9gPECMxyRiM,288
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=WNUmAFuBPcqQPVgt6AS1ldy8Be2XO01N-1YQL__Q6ZY,832
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=xhjLwr8hiikEXKy8APMy0Bmvtz1g0WnG84gX7e9lArs,957
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=KCLg-pXpns9cnKDXKN2WH6mV41OH-6cbT-5zKQotSdw,526
matplotlib/mpl-data/stylelib/seaborn-bright.mplstyle,sha256=pDqn3-NUyVLvlfkYs8n8HzNZvmslVMChkeH-HtZuJIc,144
matplotlib/mpl-data/stylelib/seaborn-colorblind.mplstyle,sha256=eCSzFj5_2vR6n5qu1rHE46wvSVGZcdVqz85ov40ZsH8,148
matplotlib/mpl-data/stylelib/seaborn-dark-palette.mplstyle,sha256=p5ABKNQHRG7bk4HXqMQrRBjDlxGAo3RCXHdQmP7g-Ng,142
matplotlib/mpl-data/stylelib/seaborn-dark.mplstyle,sha256=I4xQ75vE5_9X4k0cNDiqhhnF3OcrZ2xlPX8Ll7OCkoE,667
matplotlib/mpl-data/stylelib/seaborn-darkgrid.mplstyle,sha256=2bXOSzS5gmPzRBrRmzVWyhg_7ZaBRQ6t_-O-cRuyZoA,670
matplotlib/mpl-data/stylelib/seaborn-deep.mplstyle,sha256=44dLcXjjRgR-6yaopgGRInaVgz3jk8VJVQTbBIcxRB0,142
matplotlib/mpl-data/stylelib/seaborn-muted.mplstyle,sha256=T4o3jvqKD_ImXDkp66XFOV_xrBVFUolJU34JDFk1Xkk,143
matplotlib/mpl-data/stylelib/seaborn-notebook.mplstyle,sha256=PcvZQbYrDdducrNlavBPmQ1g2minio_9GkUUFRdgtoM,382
matplotlib/mpl-data/stylelib/seaborn-paper.mplstyle,sha256=n0mboUp2C4Usq2j6tNWcu4TZ_YT4-kKgrYO0t-rz1yw,393
matplotlib/mpl-data/stylelib/seaborn-pastel.mplstyle,sha256=8nV8qRpbUrnFZeyE6VcQ1oRuZPLil2W74M2U37DNMOE,144
matplotlib/mpl-data/stylelib/seaborn-poster.mplstyle,sha256=dUaKqTE4MRfUq2rWVXbbou7kzD7Z9PE9Ko8aXLza8JA,403
matplotlib/mpl-data/stylelib/seaborn-talk.mplstyle,sha256=7FnBaBEdWBbncTm6_ER-EQVa_bZgU7dncgez-ez8R74,403
matplotlib/mpl-data/stylelib/seaborn-ticks.mplstyle,sha256=CITZmZFUFp40MK2Oz8tI8a7WRoCizQU9Z4J172YWfWw,665
matplotlib/mpl-data/stylelib/seaborn-white.mplstyle,sha256=WjJ6LEU6rlCwUugToawciAbKP9oERFHr9rfFlUrdTx0,665
matplotlib/mpl-data/stylelib/seaborn-whitegrid.mplstyle,sha256=ec4BjsNzmOvHptcJ3mdPxULF3S1_U1EUocuqfIpw-Nk,664
matplotlib/mpl-data/stylelib/seaborn.mplstyle,sha256=_Xu6qXKzi4b3GymCOB1b1-ykKTQ8xhDliZ8ezHGTiAs,1130
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=BsirZVd1LmPWT4tBIz6loZPjZcInoQrIGfC7rvzqmJw,190
matplotlib/offsetbox.py,sha256=jl9fv9tiXIGKySyn8lpJ2YTgMmssTUgIeMWOrnzpzgM,59932
matplotlib/patches.py,sha256=G1WfAmBZqIyyxHhcXjA-UkVcOaXzU76BdrUws492zPA,148905
matplotlib/path.py,sha256=_uB4k4Q3pug51DpGj1s_qnqdHuv61CF_AjOP5Wvsn8Y,39443
matplotlib/patheffects.py,sha256=Rv4znXNBQIAdv4BWEqSYc4RKE2c80QLaiZ2hhAW6xJA,13600
matplotlib/projections/__init__.py,sha256=fd9jVOdrhQQMSDwRRsqd3cM-6y9DkYmVjpL0j1chQnI,1668
matplotlib/projections/__pycache__/__init__.cpython-38.pyc,,
matplotlib/projections/__pycache__/geo.cpython-38.pyc,,
matplotlib/projections/__pycache__/polar.cpython-38.pyc,,
matplotlib/projections/geo.py,sha256=7p4Xde6eEZVGVnoCX6QcfWJm-76I5O3VzN0E-Rxm7qA,17457
matplotlib/projections/polar.py,sha256=RpO-I6B1Qv46VAs-wobDDqwxC1_2g2aM3j3Ox9qTqvo,54200
matplotlib/pylab.py,sha256=q3UYAFKHw4_QImBim6f0M3lNvn0phmQFm7y3o_mzTvk,1691
matplotlib/pyplot.py,sha256=fnm0vOMTTd4i1boSzFJLq83EzKoorWVncWhtK8-2lQY,115709
matplotlib/quiver.py,sha256=VIFTlGPdwOh_JsT7F1YKs2ak3bFV1yCc4Orh65gPeuE,47284
matplotlib/rcsetup.py,sha256=rC6ZAr_77doqpvX7Oh7n_3wYXwxA2CxFIYrEUqJ38AA,57256
matplotlib/sankey.py,sha256=mlEsQgppYW6vyRHS6HPBxkrd4SKWnCU2l6Aji0nJafA,36338
matplotlib/scale.py,sha256=DGOmpKpjPt5uKbATq76mtG7TpDhMkMPxO6QxqbtGksU,23623
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-38.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-38.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-38.pyc,,
matplotlib/sphinxext/mathmpl.py,sha256=dUQg1fazNBSBH4-VawOMBeiQnqCK7eQtkSEkXklAW_8,3759
matplotlib/sphinxext/plot_directive.py,sha256=Ios2cPLcRw0q31JKm_A901rCj7N3QlkMzOXx5xfe0wo,26230
matplotlib/spines.py,sha256=aQNIkdauITmsLUSSamYvnp_mBkThD9w9jjb0NDYtGvg,20223
matplotlib/stackplot.py,sha256=CVmWXtEu1gvypR0H4sXqapKpH8zakW0VNHvdxuiRKYo,3908
matplotlib/streamplot.py,sha256=PWiFd6_EipDDvhcUEfoCwyAWnVKb5BrbwYSLQfRcpmw,22916
matplotlib/style/__init__.py,sha256=EExOAUAq3u_rscUwkfKtZoEgLA5npmltCrYZOP9ftjw,67
matplotlib/style/__pycache__/__init__.cpython-38.pyc,,
matplotlib/style/__pycache__/core.cpython-38.pyc,,
matplotlib/style/core.py,sha256=hNuFln6CA3tZQVnbc1lq_KnkWI1xAaniMDU8tABH0uU,8486
matplotlib/table.py,sha256=EteA95gpi0dkPFFROnRhEXL-t8rrB1Uuv48nyOnlP_4,26560
matplotlib/testing/__init__.py,sha256=6OFh9UBnCgvcMElvYfl45QwO25JstN8R6Sc-gm8NFSk,1402
matplotlib/testing/__pycache__/__init__.cpython-38.pyc,,
matplotlib/testing/__pycache__/compare.cpython-38.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-38.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-38.pyc,,
matplotlib/testing/__pycache__/disable_internet.cpython-38.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-38.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-38.pyc,,
matplotlib/testing/compare.py,sha256=18R0aDuasxy9OTPTkf0rxPfNIRdKkGvHyxkymRH4Pgs,16981
matplotlib/testing/conftest.py,sha256=LeTKHn7XJiHmnEPY7ZfsUndvoOH69d-_L906Yp3o6gA,5567
matplotlib/testing/decorators.py,sha256=PG-bOz-0F14oHh5K_6q8gGN6vce3_rbfVRKH07ezGZo,18726
matplotlib/testing/disable_internet.py,sha256=SkQvibGuw2Ey5mXcVrNayIc2MiKopomaAgGQd_OBss8,4911
matplotlib/testing/exceptions.py,sha256=72QmjiHG7DwxSvlJf8mei-hRit5AH3NKh0-osBo4YbY,138
matplotlib/testing/jpl_units/Duration.py,sha256=dmC4GMu1reBR5N5b8bEUxWuoBQPQJ3p1tZEcEtMcn-8,4458
matplotlib/testing/jpl_units/Epoch.py,sha256=6KVs5AVJtxRhLxbEwvr4RO3SmWIRX3NK8-6jY1mzGRg,6350
matplotlib/testing/jpl_units/EpochConverter.py,sha256=LVvvSWxlyhpWzaWP6qIM-J1pT_NavtRxKeFL0tPQmNI,3165
matplotlib/testing/jpl_units/StrConverter.py,sha256=qJQW3lwFdTTu6VbhuR4woJdvRWwMX-Yc5HyQrHFr_hU,2953
matplotlib/testing/jpl_units/UnitDbl.py,sha256=nXFWa8Oloovg4gi4rbTRgMC36vedJjxJsXAuizr1LYA,7611
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=3GqeyY8rdw0osr66QNd9PwxqsrOfRM0B_tLXemcptnE,3099
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=CRcbPtE3K0FlFJ4hkhi-SgQl1MUV-VlmIeOPIEPNwuI,681
matplotlib/testing/jpl_units/__init__.py,sha256=p__9RUwrt2LJ2eoT2JPM-42XLxSJrfA4az3rN5uP6d4,2684
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-38.pyc,,
matplotlib/testing/widgets.py,sha256=GXZIU41tAPvyJ3VkTqT64rZA582zX1vDTFs_jA2ZT-g,1509
matplotlib/tests/__init__.py,sha256=ns6SIKdszYNXD5h5PqKRCR06Z45H-sXrUX2VwujSRIM,366
matplotlib/tests/__pycache__/__init__.cpython-38.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-38.pyc,,
matplotlib/tests/conftest.py,sha256=QtpdWPUoXL_9F8WIytDc3--h0nPjbo8PToig7svIT1Y,258
matplotlib/tests/test_afm.py,sha256=DGVfvABg6FRmbAq2ldRhM2wlqNfVrmRtSz12MCyqDXk,3710
matplotlib/tests/test_agg.py,sha256=Rkm7_LGXyKrzW3GwKwN0mUBFdlAzUZyHplK7fHwTZRI,7429
matplotlib/tests/test_agg_filter.py,sha256=sfntvGVUuCaGqU3DdOnTRXqq0P1afVqWuaV_ZEYh8kQ,969
matplotlib/tests/test_animation.py,sha256=JCjOkj2faWxRxzvs2dXg4djvKTzQvcSEHy2Q-hyKSZs,8354
matplotlib/tests/test_arrow_patches.py,sha256=ZzLXyQQ3c33BopDLEu0WL5UpS2nohSqzvDnFcWEiQg8,5660
matplotlib/tests/test_artist.py,sha256=JUjHpSr52wq28ax9n9VuXlKsbp3UvJ0e_n9j5EtZhOA,9061
matplotlib/tests/test_axes.py,sha256=7VukQrnp878Uxpp1Mpe1wTAIuePfa8Oz6wGaqHoqLg8,211271
matplotlib/tests/test_backend_bases.py,sha256=xK0X4-U1zptpB0yuE0XYibZazJ0kfumsHBgZZR9g1TE,5935
matplotlib/tests/test_backend_cairo.py,sha256=O2LTYjsfPn__bKtTz4MGGBodpSshoPkzu0INsc18xmI,1821
matplotlib/tests/test_backend_nbagg.py,sha256=ydzDqVZEPJNdGBoyrORhkV73B3RbGvDNLGyJqpz2XtE,907
matplotlib/tests/test_backend_pdf.py,sha256=vkiBnItAQ4pBE7JT3I5r2O3hISgCP1sn9w7i5WYtjWo,8668
matplotlib/tests/test_backend_pgf.py,sha256=f6XzRLXTTKl25EWINt4nVydecrz12nFNfaNe4lQf2J8,10320
matplotlib/tests/test_backend_ps.py,sha256=66ekBUkVCcIJnk1G15_8D2HTkRnLWuO6XLwal6RYe1A,4588
matplotlib/tests/test_backend_qt.py,sha256=KAZGcYW3KP8HdI0R0U_ufihq8X21NhPaHsle2Fwb7IM,9211
matplotlib/tests/test_backend_svg.py,sha256=LLtBDkEZdu-EwqWBz8eZAPqEKoKdoYHTepNNpdpaNSI,12443
matplotlib/tests/test_backend_tk.py,sha256=kQRNkil_iGDN2fykGOdLogY6oYqylIsuOWBPRnKqeWk,1402
matplotlib/tests/test_backend_tools.py,sha256=C-B7NCkyWsQ5KzQEnI5Be16DsAHHZJU9P5v9--wsF-o,501
matplotlib/tests/test_backend_webagg.py,sha256=u-UvO04iqxZbFlUrNIfyD0KIAxEhSOyqlrOqxO4DnDw,702
matplotlib/tests/test_backends_interactive.py,sha256=bab-LUJxQmnxLLdNyBh0jPdiDpJDRmg2PKHQNprLBT4,7875
matplotlib/tests/test_basic.py,sha256=cYBVq3xiXayYibUunhXmGOLQ7p9v33LjCB0mSDxull4,1201
matplotlib/tests/test_bbox_tight.py,sha256=0Oh66vu8R1o2Zid9G97H6wa_ezUuegz_V5ubYmkxOXw,4949
matplotlib/tests/test_category.py,sha256=qvYupN49Eo-GQeglvBoE9VjOmVamZ3JBI30fXaNhTjM,10223
matplotlib/tests/test_cbook.py,sha256=Bx9DBq5Aa7ybydjIcKRnlsPiwK61qz5RAfnoxT5Rkdg,24628
matplotlib/tests/test_collections.py,sha256=4xx5fi9DuyYP9jMjy2j0tYfWhWjhPcSNJ5vgAzXrDHA,23008
matplotlib/tests/test_colorbar.py,sha256=jyBsfRk4fxuWdass5dEMi43YkkaHuIUa_iQ0VeQV8-U,22652
matplotlib/tests/test_colors.py,sha256=IyU0S_tw4jSSlxJ0GW3hzXj37YydcELax9HGdZVXL7Y,40696
matplotlib/tests/test_compare_images.py,sha256=L5-LlwjqGqZG5pxpiXKcoF2K1X4d0fTe3ZtaE8ITwZk,3294
matplotlib/tests/test_constrainedlayout.py,sha256=IDEEg76sM28n2M8vfVcBYTwpsKsbeWflqvLE9yAGHRc,13052
matplotlib/tests/test_container.py,sha256=ijZ2QDT_GLdMwKW393K4WU6R7A5jJNaH1uhLRDwoXXI,550
matplotlib/tests/test_contour.py,sha256=z60n5jCxlFEDFOcMcVNyxGNP_oPytf5eQzZ6tQMzWo4,13831
matplotlib/tests/test_cycles.py,sha256=_R4kd5eVok6QimW8NTtk65oNG9mNHCOIz1AFSFBSgx4,5660
matplotlib/tests/test_dates.py,sha256=pDXEFsbedmY3ocAPbL8MJNVVL2v3B5H-jvk-_0tsaKE,38932
matplotlib/tests/test_determinism.py,sha256=bu8E_NMdeN0X2KRMStjAgK3tklL5SG_IqYu4hov6Z-4,4586
matplotlib/tests/test_dviread.py,sha256=XawbcMIJrT5N1AyA_c3VaaOAT5b6Ym6KyqIVo3MQlRg,2313
matplotlib/tests/test_figure.py,sha256=XR16rrkuNP2-nbi1R3U-ZOYFg-oSnveaTF0gwj9gQ9w,25599
matplotlib/tests/test_font_manager.py,sha256=dlshasWEzysAXM53Ba4vqgMF9B-clFPdva6odX7_rpY,7615
matplotlib/tests/test_fontconfig_pattern.py,sha256=JI2E0Jz1P_ByzhP5pEUm7Eu6szyWePvoJr86_QP0sAY,2021
matplotlib/tests/test_gridspec.py,sha256=OOCSE5R6xq3XI8Bstm0xMR1hv4guHFBCAwfCXrk_Td0,960
matplotlib/tests/test_image.py,sha256=8mwmaav2wPZUHfr_jvbKgaav9cP-US8V_gKz3xj5kjo,35970
matplotlib/tests/test_legend.py,sha256=aVztwy1LjsXhG2htqFmL9SqNGAENwbBoZnfpPN-MXwQ,22891
matplotlib/tests/test_lines.py,sha256=gwjxoqaV0XBhsQvyjdxIrygfZSQtd7yXrPt1ToYRIqc,8337
matplotlib/tests/test_marker.py,sha256=ejCW_5hY778KhuU9C_P6WKUZGoa59kozok__VESL7GE,6436
matplotlib/tests/test_mathtext.py,sha256=xnfd8GR34hYqhiaEJXPEnYHJUZXlLnsxjug6OvUQMLg,14629
matplotlib/tests/test_matplotlib.py,sha256=ubT3PYR5foBLDJoRo0BaRpBAWrwuz-qTWVOsSWtPSLQ,1455
matplotlib/tests/test_mlab.py,sha256=cdhtXCBn2W-TSXbHPM1RJgQG72v4iaeqeUFlNPox5Mo,65684
matplotlib/tests/test_offsetbox.py,sha256=xRIOdVPDWz2gTGdncmf4vq1qDo2ievgQw3sF9exOUvI,10828
matplotlib/tests/test_patches.py,sha256=dyQDtw4buHEYxMR8F0jvQrO-nVXINX1T_F9k9BwJQOQ,19273
matplotlib/tests/test_path.py,sha256=Ment4_Y6SKJ5ZBeUqLAhlBd1Udue2RtVXCU2lF_YJ3s,16254
matplotlib/tests/test_patheffects.py,sha256=FvzVNN3BlgYzCI7Jcsf0GqnUvUhHF6Zg7S2QW2JXJHE,5202
matplotlib/tests/test_pickle.py,sha256=1lDbWJcPpAK3rVGQG9KLWUb8qmAT9q8FmXmf6zKyG6c,5626
matplotlib/tests/test_png.py,sha256=AnAGf7l-Nauh5mB-6sNQpI5NyAFwiQ06wj6DTgAdzK8,1300
matplotlib/tests/test_polar.py,sha256=nVnuPrUO1suPN_1lRHrrW-EYug40clk6ywf_tjkeO-A,11730
matplotlib/tests/test_preprocess_data.py,sha256=BmIpKQGlP5CBiJ9fQIToLKKEwj_g-zrCanUwEzSYcUI,10321
matplotlib/tests/test_pyplot.py,sha256=H8TVYx0c4jebucHHx5JpybjPDPdsnNVhH90mmwOfjnw,2481
matplotlib/tests/test_quiver.py,sha256=CcOtckFlLdWKfNbC6y84P_UWM3AciBWsOqHKwrx12yU,8060
matplotlib/tests/test_rcparams.py,sha256=XuaWHNwmYmvPbHrgyXrOlNLejmPORRSRma3_HGqCzO4,19271
matplotlib/tests/test_sankey.py,sha256=KdWhgZpEiCJ8o7yuFjnCgqFeodwKSCuuhV_mhIc04dM,310
matplotlib/tests/test_scale.py,sha256=d0fNtLZw-cMkv6h2_0FJSwdreMY-_8pknATmntmXKzM,5659
matplotlib/tests/test_simplification.py,sha256=_dIImjiA0VHBEbOrGQluQKCS6SQFwEVvfL9pqIn_SHI,11048
matplotlib/tests/test_skew.py,sha256=quEWYGkr2oPbTq0cnU9uydaZakninKCX08HX2aRFwYI,6306
matplotlib/tests/test_sphinxext.py,sha256=5rKrJNwt90aT-dSftCDG_2M6wmg91g4eK5sAQLhnYaw,1969
matplotlib/tests/test_spines.py,sha256=5jGy42khH7ndaHRCgJONXBOvM5ZnrzOhPSR42IAUAA0,3132
matplotlib/tests/test_streamplot.py,sha256=LW7JMfYvlwznp4bLEDp7oAK-xZN7e7O5fqEcNxOUW4U,3823
matplotlib/tests/test_style.py,sha256=xuj6w7q49hgID0bWJp0FRKnzOVzcIhmZi3VWOMia004,5727
matplotlib/tests/test_subplots.py,sha256=ZIivQ4SME_kNWS2I5c2g5jcQ9ZFGx1A3DfMyixECBzk,5980
matplotlib/tests/test_table.py,sha256=SC3HPD07aUThw6RR4hohsMYVlDaqwMBE9-XV-bsgQjY,5729
matplotlib/tests/test_testing.py,sha256=qXLI2etvF964zK9tviBk9XOQywzV5mtiNk7bVY02DG8,629
matplotlib/tests/test_texmanager.py,sha256=EXoFPnBJ2hI2tPI1DRsP9CNzqzonzJOEQbJ4TQAoo5I,457
matplotlib/tests/test_text.py,sha256=AQ30w8uTRFWxmxEAu_yfesPBcInHIoXkvIFrt5Jphqg,22408
matplotlib/tests/test_ticker.py,sha256=VX3qv_PJMBXVLoXojWf2hd66wIiS53B4pSCnHDpb9uI,51441
matplotlib/tests/test_tightlayout.py,sha256=OerbmOkNl5f12pLcHnJDSt-pm1njdUSOacuPx9I9QM0,10307
matplotlib/tests/test_transforms.py,sha256=FqqjsCyv7wCCvlFgd18i1ZMz72_SGmB9im-6qyNOEJw,27288
matplotlib/tests/test_triangulation.py,sha256=wXdBV3Uo0EKi0ct4JG-wct2VzjigAdx2ZDQvnOKe3gQ,45946
matplotlib/tests/test_ttconv.py,sha256=yW3YkGWTh-h9KXcyy70dEH4ytVuhMlzl-HMsNbK_gX8,540
matplotlib/tests/test_type1font.py,sha256=29r5f9PEPTDaWiJ8X3KMVAMPuL2zqFxcVNtnuAC5emI,2088
matplotlib/tests/test_units.py,sha256=BmRHlRdxQYcen5QYRFc6bMM6auHp7nTMPKg644U-S0M,5715
matplotlib/tests/test_usetex.py,sha256=wvQAFmv8zIFU1amXEtHwk4s-SFGoc3U5tyoU_yG3FOs,2899
matplotlib/tests/test_widgets.py,sha256=Sbbk245D5hnWmdfB6Z971ouLO6GfNlbgEHlcyXbnCKY,16208
matplotlib/texmanager.py,sha256=yK-n17gjWh1sktKXfkw_gNxJiqQMOPCmsTq_2c39AUs,15868
matplotlib/text.py,sha256=7DFZLul_AL-XhBwIU4X6S3gF_1kKp2KLgtwOBEyd1PQ,66092
matplotlib/textpath.py,sha256=FXZRkSYAUvRGKyOph7ftlDl7Fa8MHNgvmXDCUgy1sWA,14982
matplotlib/ticker.py,sha256=9c91TqbksiTfImZheJy3jNUWS16BtTAUeBTMqD62_mc,105001
matplotlib/tight_bbox.py,sha256=uSTY0z5feK2ef6OSR2MQAW0UgobRk4qhq5mNvOOxfaA,2935
matplotlib/tight_layout.py,sha256=PMspr2TVY5y9p1LxxXyjVabwo885A6FGn3uiGteqUkk,13156
matplotlib/transforms.py,sha256=Q-jAY54C8PMgGyajJEwZlFtUGfux_l9uexxYSFTgiow,96797
matplotlib/tri/__init__.py,sha256=XMaejh88uov7Neu7MuYMyaNQqaxg49nXaiJfvjifrRM,256
matplotlib/tri/__pycache__/__init__.cpython-38.pyc,,
matplotlib/tri/__pycache__/triangulation.cpython-38.pyc,,
matplotlib/tri/__pycache__/tricontour.cpython-38.pyc,,
matplotlib/tri/__pycache__/trifinder.cpython-38.pyc,,
matplotlib/tri/__pycache__/triinterpolate.cpython-38.pyc,,
matplotlib/tri/__pycache__/tripcolor.cpython-38.pyc,,
matplotlib/tri/__pycache__/triplot.cpython-38.pyc,,
matplotlib/tri/__pycache__/trirefine.cpython-38.pyc,,
matplotlib/tri/__pycache__/tritools.cpython-38.pyc,,
matplotlib/tri/triangulation.py,sha256=MLYgUszhjc4Ns8PU-nqJooYhjHkQAIhO8PoYmvmig_k,8326
matplotlib/tri/tricontour.py,sha256=73zQDubtylXr2-Q2MQT04uH4l5Xsa8OkgKwvcUrxyEk,11314
matplotlib/tri/trifinder.py,sha256=juA5OPBiK2v58Hm9_h2Z93X88hWMvuzbqSN-var7fFg,3468
matplotlib/tri/triinterpolate.py,sha256=6Sct0C6ebySPm1Df_n_uZTnQYK9mX4rUHMawsRQi5q0,64375
matplotlib/tri/tripcolor.py,sha256=tOWZYjwe16QhkzEU_zoPyATjT3y2LjXwT2_zz9S8kJ4,5007
matplotlib/tri/triplot.py,sha256=bFMLKxPqw4GDzja0iZE6NId0QXpx8JFBkoNl0eGx9F8,2763
matplotlib/tri/trirefine.py,sha256=82eMuX5pXcAD5gVmfmjCv9A5rlhtlFVip9ZLL25TTuw,13222
matplotlib/tri/tritools.py,sha256=fZSYLphtOV1Gd2F07a_WQyEdQrkbfwm18NehQP6GDXo,10579
matplotlib/ttconv.py,sha256=YHcvd5xEeHrvPBLwCmwUruWtVlY8xM6-TbA5mQpNUxE,239
matplotlib/type1font.py,sha256=GiD06VYI-CP4jjbqshyHXdMDXztkctNjfQa-r_wZnIY,12330
matplotlib/units.py,sha256=oD3Vz9QZuwzN9xMQRBzqxhfK-43SrR-I1rOY0mh2xYk,7275
matplotlib/widgets.py,sha256=ocwoFqsxtADASvvtLnRYFNfC8HxjhPebNStx9aKJsJU,93210
mpl_toolkits/axes_grid/__init__.py,sha256=VLlc0DaOkr9JumPa8W4zt9lGHp180ie8_WLPZVNSJMw,537
mpl_toolkits/axes_grid/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/anchored_artists.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/angle_helper.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_divider.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_grid.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_rgb.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_size.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axis_artist.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axisline_style.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/axislines.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/clip_path.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/colorbar.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/floating_axes.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/grid_finder.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/grid_helper_curvelinear.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/inset_locator.cpython-38.pyc,,
mpl_toolkits/axes_grid/__pycache__/parasite_axes.cpython-38.pyc,,
mpl_toolkits/axes_grid/anchored_artists.py,sha256=_F6-9iacZidb5JpJ8jCOZ9PdiZaR5qpfBjf-3VjTzNc,291
mpl_toolkits/axes_grid/angle_helper.py,sha256=Tb4Mb_NGkUdkisebe2dqfBdFmUZiSmGyUnftiSeSIls,51
mpl_toolkits/axes_grid/axes_divider.py,sha256=tJlPia3Z8xLq6uXehBwAlD_4ywMvRTTkM73qNnCpo7Q,178
mpl_toolkits/axes_grid/axes_grid.py,sha256=UPlVDwsze_w2aZeLaMg4WZVK3q2EvWePXTFZFvjCQz4,89
mpl_toolkits/axes_grid/axes_rgb.py,sha256=LFo4FEXTM2E-zxE8cuYRFFzDADdLoKyzm-VNOkSX7AU,47
mpl_toolkits/axes_grid/axes_size.py,sha256=v4Nhxe7DVp1FkKX03DqJJ1aevDanDvgKT9r0ouDzTxw,48
mpl_toolkits/axes_grid/axis_artist.py,sha256=zUlJFUHueDsMtzLi_mK2_Wf-nSBQgiTsMOFpo_SngZ0,50
mpl_toolkits/axes_grid/axisline_style.py,sha256=lNVHXkFWhSWPXOOfF-wlVkDPzmzuStJyJzF-NS5Wf_g,53
mpl_toolkits/axes_grid/axislines.py,sha256=kVyhb6laiImmuNE53QTQh3kgxz0sO1mcSMpnqIdjylA,48
mpl_toolkits/axes_grid/clip_path.py,sha256=s-d36hUiy9I9BSr9wpxjgoAACCQrczHjw072JvArNvE,48
mpl_toolkits/axes_grid/colorbar.py,sha256=DckRf6tadLeTNjx-Zk1u3agnSGZgizDjd0Dxw1-GRdw,171
mpl_toolkits/axes_grid/floating_axes.py,sha256=i35OfV1ZMF-DkLo4bKmzFZP6LgCwXfdDKxYlGqjyKOM,52
mpl_toolkits/axes_grid/grid_finder.py,sha256=Y221c-Jh_AFd3Oolzvr0B1Zrz9MoXPatUABQdLsFdpw,50
mpl_toolkits/axes_grid/grid_helper_curvelinear.py,sha256=nRl_B-755X7UpVqqdwkqc_IwiTmM48z3eOMHuvJT5HI,62
mpl_toolkits/axes_grid/inset_locator.py,sha256=qqXlT8JWokP0kV-8NHknZDINtK-jbXfkutH_1tcRe_o,216
mpl_toolkits/axes_grid/parasite_axes.py,sha256=rOJ8sBoR-stf1Y2PsZBDJOqk_76yN9MHHU8MYzBkOHI,438
mpl_toolkits/axes_grid1/__init__.py,sha256=-lw0ZfG4XUpuAolCpXKFwtS3w1LJ1ZToSEC9OSmB-4Q,204
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/colorbar.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-38.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=8vkx5HIRj6mfxuKigC0Dg_GTNmDCIt6kUbhre4vOSkg,20166
mpl_toolkits/axes_grid1/axes_divider.py,sha256=p0FnHJdtU0TKOsY5C2vWJwmkLC8t9o_EHPlyh_Gl9a4,25804
mpl_toolkits/axes_grid1/axes_grid.py,sha256=5-P14KAlDz2gOTDjd9FVEfVsQvBcy1VJ6xfAdzT91I8,23602
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=PhVPj1798xETTBkdW0mSOSePrx5C180PLfgOWBS6h1Y,5168
mpl_toolkits/axes_grid1/axes_size.py,sha256=b4wos0_YJgpa8qv3ysyUuIkYgtNQEW0ryOrW3XWj0jU,7550
mpl_toolkits/axes_grid1/colorbar.py,sha256=xOIxNLXACuBg-bOGqrTddtg-jhaNvxQPMoaRgegD0qM,27914
mpl_toolkits/axes_grid1/inset_locator.py,sha256=Wb1n8_DB8iiOpb70lN7bef4sPh0wHFTh5AUbd77ApMA,23112
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=MJVYUN4YRtTWrq1wmyv_y61O002tiyFESmBSaJ8xkG4,4380
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=V4oZHxbTn_Dg-UShbELB5r1v2RANT99rLg3_V_yIEog,14399
mpl_toolkits/axisartist/__init__.py,sha256=N-qlxUsIBuEcYaRNEEEzVDjHFFdIIDxJCMScIObcdNs,715
mpl_toolkits/axisartist/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/clip_path.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-38.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=qTg0PVLbblshwC0iQI5aTqEODLGY35BqPP3AZIZBCkY,13213
mpl_toolkits/axisartist/axes_divider.py,sha256=baPCBjM20SvAUeMjhvlS_cccRSM1y7ZKybtoW8upo2k,127
mpl_toolkits/axisartist/axes_grid.py,sha256=d1gBlfPI592V5MjOOj-a5pM6RmF2LDoJpLloP7CJ-oo,347
mpl_toolkits/axisartist/axes_rgb.py,sha256=cybzNZApLXFM_oZ922j7eBBZFW_qHTyieKCf5VKHAkM,183
mpl_toolkits/axisartist/axis_artist.py,sha256=uyJ_Hd-EDjsYnLOnb_ZyYO_WlWuYtZhYmxBRlHyWydU,42066
mpl_toolkits/axisartist/axisline_style.py,sha256=20_j6V4tJRTEKJAKeTsG8Oxc5O4mc7u2dNYYLrzGMEU,5039
mpl_toolkits/axisartist/axislines.py,sha256=FekRY5Nl6MpChNekBU9kVlu1TyGT403QRAq6xoLAskk,19779
mpl_toolkits/axisartist/clip_path.py,sha256=LE_IIP0byNr5ELJlD8_8fsAh215MUDoK19-BISuFB80,3777
mpl_toolkits/axisartist/floating_axes.py,sha256=D2M4qAIsdfozLUMiF0gkckFC6yg5WVOGeuG_JtORdfY,12861
mpl_toolkits/axisartist/grid_finder.py,sha256=zJP1WiMww6uMJz0lhKTBVoyPzxmLZz1klMuzf8C8jFU,10967
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=ge7S2Xdr7XGizvG1D7feqeb5hXOUBP5AjKn_tz6a2E0,14258
mpl_toolkits/axisartist/parasite_axes.py,sha256=BOUAQOChzmyvXUakE1cVjEjq5gmzMKXlQ4HfKQaMmoo,415
mpl_toolkits/mplot3d/__init__.py,sha256=V2iPIP9VyRhoJsFWnQf5AkfyI1GSSP9H6hICEe9edJo,27
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=zlc2Yi1g2W16H27hhq1dmrqjdagSCvi0aM0ZlNHMmyY,29244
mpl_toolkits/mplot3d/axes3d.py,sha256=06QHfxU0jFfHkGyljaAD-bpV8YbR7H0RdnyUkNo1Xew,103538
mpl_toolkits/mplot3d/axis3d.py,sha256=eN-4LZDksVer0Et5qvcBWp_vVxPVE0b0mJtIBshgmUM,18811
mpl_toolkits/mplot3d/proj3d.py,sha256=_hVJ_LhfXA6d9svtX8n3IWmRAFEfzKkFD-z150_L5oY,4266
mpl_toolkits/tests/__init__.py,sha256=Ox41zElZt1Po-41lx14-gMFr9R1DEK6Amt64Hn5d6sY,365
mpl_toolkits/tests/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/conftest.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axes_grid.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axes_grid1.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_angle_helper.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_axis_artist.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_axislines.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_clip_path.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_floating_axes.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_grid_finder.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_grid_helper_curvelinear.cpython-38.pyc,,
mpl_toolkits/tests/__pycache__/test_mplot3d.cpython-38.pyc,,
mpl_toolkits/tests/conftest.py,sha256=Ph6QZKdfAnkPwU52StddC-uwtCHfANKX1dDXgtX122g,213
mpl_toolkits/tests/test_axes_grid.py,sha256=oYXRFhdRo1MMjFxaoI3Bx-6w4lkiS8VkCKYk1DiGk0U,2451
mpl_toolkits/tests/test_axes_grid1.py,sha256=6kdEeSooNT0Kj7oGsbP0z2CjOpvpiEMG8tcFpQZ_HFs,18091
mpl_toolkits/tests/test_axisartist_angle_helper.py,sha256=PwhJwBm2kk4uMyhdO5arQs8IlqSX2vN0hvUzI7YHqrw,5670
mpl_toolkits/tests/test_axisartist_axis_artist.py,sha256=N4Khx8jSxkoiMz3KvumodmFKHZUtdwtjkzxLWPSdyuw,3008
mpl_toolkits/tests/test_axisartist_axislines.py,sha256=XRlLT8Hx0X3Pn0JnmXFP3hnnImiB1RIKt_17MyOTnyA,2442
mpl_toolkits/tests/test_axisartist_clip_path.py,sha256=afS3nvNqCgvDpJdg_MvbwydtSWv5b6ciP-Iq2aNcNFQ,1004
mpl_toolkits/tests/test_axisartist_floating_axes.py,sha256=xENnUpFU8EHPgnON6W1xqMVWIq8qxIzuGf1oMmSMFJo,4127
mpl_toolkits/tests/test_axisartist_grid_finder.py,sha256=e65sLudWFIXeU08Sis3_SI1JEI6eq8YqKj-80F_Nohk,325
mpl_toolkits/tests/test_axisartist_grid_helper_curvelinear.py,sha256=w0jJQs1uTSQEmEi4sMUkFdm6rvgD-T1TVM7Ck3p3U8E,7516
mpl_toolkits/tests/test_mplot3d.py,sha256=Va1upO2Fj90ilRGaHx4wog6q6Ukec_HSSQRzL1HrBzQ,35996
pylab.py,sha256=u_By3CHla-rBMg57egFXIxZ3P_J6zEkSu_dNpBcH5pw,90
