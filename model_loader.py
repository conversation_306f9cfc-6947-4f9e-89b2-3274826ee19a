#!/usr/bin/env python3
"""
DysplaciaNet Model Loader and Metrics Extractor

This script loads the DysplaciaNet model and extracts comprehensive metrics
including model architecture, parameters, layer information, and performance metrics.

Author: AI Assistant
Date: 2025
"""

import os
import json
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.models import model_from_json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings

# Suppress TensorFlow warnings for cleaner output
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

class DysplaciaNetModelLoader:
    """
    A comprehensive model loader and analyzer for DysplaciaNet.
    
    This class provides functionality to:
    - Load the DysplaciaNet model from JSON and weights files
    - Extract detailed model metrics and architecture information
    - Analyze model layers and parameters
    - Generate comprehensive reports
    """
    
    def __init__(self, model_dir: str = "DysplaciaNet_Interpretability_Study-master/model"):
        """
        Initialize the model loader.
        
        Args:
            model_dir (str): Directory containing model files
        """
        self.model_dir = Path(model_dir)
        self.model = None
        self.model_json_path = self.model_dir / "final_model.json"
        self.model_weights_path = self.model_dir / "final_model_weights.h5"
        self.metrics = {}
        
    def load_model(self) -> bool:
        """
        Load the DysplaciaNet model from JSON architecture and weights files.
        
        Returns:
            bool: True if model loaded successfully, False otherwise
        """
        try:
            print("🔄 Loading DysplaciaNet model...")
            
            # Check if model files exist
            if not self.model_json_path.exists():
                print(f"❌ Model JSON file not found: {self.model_json_path}")
                return False
                
            if not self.model_weights_path.exists():
                print(f"❌ Model weights file not found: {self.model_weights_path}")
                return False
            
            # Load model architecture from JSON
            print("📄 Loading model architecture...")
            with open(self.model_json_path, 'r') as json_file:
                model_json = json_file.read()
            
            # Create model from JSON
            self.model = model_from_json(model_json)
            
            # Load weights
            print("⚖️  Loading model weights...")
            self.model.load_weights(str(self.model_weights_path))
            
            # Compile model with appropriate loss and metrics
            print("🔧 Compiling model...")
            self.model.compile(
                optimizer='adam',
                loss='binary_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
            
            print("✅ Model loaded successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error loading model: {str(e)}")
            return False
    
    def extract_basic_metrics(self) -> Dict:
        """
        Extract basic model metrics and architecture information.
        
        Returns:
            Dict: Dictionary containing basic model metrics
        """
        if self.model is None:
            print("❌ Model not loaded. Please load model first.")
            return {}
        
        print("📊 Extracting basic model metrics...")
        
        basic_metrics = {
            'model_name': 'DysplaciaNet',
            'input_shape': self.model.input_shape,
            'output_shape': self.model.output_shape,
            'total_params': self.model.count_params(),
            'trainable_params': sum([tf.keras.backend.count_params(w) for w in self.model.trainable_weights]),
            'non_trainable_params': sum([tf.keras.backend.count_params(w) for w in self.model.non_trainable_weights]),
            'total_layers': len(self.model.layers),
            'model_type': 'Binary Classification',
            'task': 'Dysplastic vs Normal Cell Classification'
        }
        
        self.metrics['basic'] = basic_metrics
        return basic_metrics
    
    def extract_layer_metrics(self) -> Dict:
        """
        Extract detailed information about each layer in the model.
        
        Returns:
            Dict: Dictionary containing layer-wise metrics
        """
        if self.model is None:
            print("❌ Model not loaded. Please load model first.")
            return {}
        
        print("🔍 Extracting layer-wise metrics...")
        
        layer_info = []
        layer_types = {}
        
        for i, layer in enumerate(self.model.layers):
            layer_data = {
                'layer_index': i,
                'layer_name': layer.name,
                'layer_type': type(layer).__name__,
                'output_shape': str(layer.output_shape),
                'param_count': layer.count_params(),
                'trainable': layer.trainable
            }
            
            # Add specific information based on layer type
            if hasattr(layer, 'activation'):
                layer_data['activation'] = str(layer.activation).split()[1] if hasattr(layer.activation, '__name__') else str(layer.activation)
            
            if hasattr(layer, 'filters'):
                layer_data['filters'] = layer.filters
                
            if hasattr(layer, 'kernel_size'):
                layer_data['kernel_size'] = layer.kernel_size
                
            if hasattr(layer, 'strides'):
                layer_data['strides'] = layer.strides
                
            if hasattr(layer, 'padding'):
                layer_data['padding'] = layer.padding
            
            layer_info.append(layer_data)
            
            # Count layer types
            layer_type = type(layer).__name__
            layer_types[layer_type] = layer_types.get(layer_type, 0) + 1
        
        layer_metrics = {
            'layers': layer_info,
            'layer_type_counts': layer_types,
            'total_layers': len(layer_info)
        }
        
        self.metrics['layers'] = layer_metrics
        return layer_metrics
    
    def get_model_summary(self) -> str:
        """
        Get a detailed model summary as string.
        
        Returns:
            str: Model summary
        """
        if self.model is None:
            return "Model not loaded."
        
        # Capture model summary
        import io
        import sys
        
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()
        
        self.model.summary()
        
        sys.stdout = old_stdout
        summary = buffer.getvalue()
        
        return summary
    
    def print_comprehensive_metrics(self):
        """
        Print all extracted metrics in a formatted way.
        """
        if not self.metrics:
            print("❌ No metrics available. Please extract metrics first.")
            return
        
        print("\n" + "="*80)
        print("🧠 DYSPLACIANET MODEL COMPREHENSIVE METRICS")
        print("="*80)
        
        # Basic metrics
        if 'basic' in self.metrics:
            basic = self.metrics['basic']
            print(f"\n📋 BASIC MODEL INFORMATION:")
            print(f"   Model Name: {basic['model_name']}")
            print(f"   Task: {basic['task']}")
            print(f"   Model Type: {basic['model_type']}")
            print(f"   Input Shape: {basic['input_shape']}")
            print(f"   Output Shape: {basic['output_shape']}")
            print(f"   Total Parameters: {basic['total_params']:,}")
            print(f"   Trainable Parameters: {basic['trainable_params']:,}")
            print(f"   Non-trainable Parameters: {basic['non_trainable_params']:,}")
            print(f"   Total Layers: {basic['total_layers']}")
        
        # Layer metrics
        if 'layers' in self.metrics:
            layers = self.metrics['layers']
            print(f"\n🔍 LAYER ANALYSIS:")
            print(f"   Total Layers: {layers['total_layers']}")
            print(f"   Layer Type Distribution:")
            for layer_type, count in layers['layer_type_counts'].items():
                print(f"     - {layer_type}: {count}")
        
        print("\n" + "="*80)
    
    def save_metrics_to_file(self, filename: str = "dysplacianet_metrics.json"):
        """
        Save extracted metrics to a JSON file.
        
        Args:
            filename (str): Output filename
        """
        if not self.metrics:
            print("❌ No metrics to save. Please extract metrics first.")
            return
        
        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, tuple):
                return list(obj)
            return obj
        
        # Deep convert the metrics dictionary
        def deep_convert(data):
            if isinstance(data, dict):
                return {key: deep_convert(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [deep_convert(item) for item in data]
            else:
                return convert_numpy_types(data)
        
        converted_metrics = deep_convert(self.metrics)
        
        try:
            with open(filename, 'w') as f:
                json.dump(converted_metrics, f, indent=2)
            print(f"✅ Metrics saved to {filename}")
        except Exception as e:
            print(f"❌ Error saving metrics: {str(e)}")


def main():
    """
    Main function to demonstrate the model loader functionality.
    """
    print("🚀 DysplaciaNet Model Loader and Metrics Extractor")
    print("="*60)
    
    # Initialize model loader
    loader = DysplaciaNetModelLoader()
    
    # Load model
    if not loader.load_model():
        print("❌ Failed to load model. Exiting.")
        return
    
    # Extract all metrics
    print("\n📊 Extracting comprehensive metrics...")
    loader.extract_basic_metrics()
    loader.extract_layer_metrics()
    
    # Print metrics
    loader.print_comprehensive_metrics()
    
    # Save metrics to file
    loader.save_metrics_to_file()
    
    # Print model summary
    print("\n📋 MODEL SUMMARY:")
    print("-" * 60)
    print(loader.get_model_summary())
    
    print("\n✅ Model analysis complete!")


if __name__ == "__main__":
    main()
