#!/usr/bin/env python3
"""
Complete DysplaciaNet Analysis Workflow

This script runs the complete analysis pipeline including:
1. Dataset visualization with polygon annotations
2. Model performance evaluation
3. Explainable AI analysis with quantitative metrics
4. Results visualization and reporting

Usage: python run_complete_analysis.py

Author: AI Assistant
Date: 2025
"""

import os
import sys
import time
from pathlib import Path

def print_header(title, char="=", width=80):
    """Print a formatted header."""
    print("\n" + char * width)
    print(f"🔬 {title}")
    print(char * width)

def check_environment():
    """Check if the environment is properly set up."""
    print("🔍 Checking environment setup...")
    
    required_files = [
        "DysplaciaNet_Interpretability_Study-master/model/final_model.json",
        "DysplaciaNet_Interpretability_Study-master/model/final_model_weights.h5",
        "data",
        "model_loader.py",
        "dataset_visualizer.py", 
        "model_evaluation_xai.py",
        "visualize_xai_results.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ Environment check passed!")
    return True

def run_step(step_name, command, description):
    """Run a single analysis step."""
    print(f"\n🔄 {step_name}: {description}")
    print(f"   Command: {command}")
    
    start_time = time.time()
    result = os.system(command)
    end_time = time.time()
    
    if result == 0:
        print(f"✅ {step_name} completed successfully in {end_time - start_time:.1f}s")
        return True
    else:
        print(f"❌ {step_name} failed with exit code {result}")
        return False

def main():
    """Main analysis workflow."""
    print_header("DYSPLACIANET COMPLETE ANALYSIS WORKFLOW")
    
    print("This script will run the complete DysplaciaNet analysis including:")
    print("• Dataset visualization with polygon annotations")
    print("• Model performance evaluation (accuracy, precision, recall, F1)")
    print("• Explainable AI analysis (Grad-CAM, Integrated Gradients)")
    print("• Quantitative XAI metrics (faithfulness, localization scores)")
    print("• Results visualization and comprehensive reporting")
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please ensure all required files are present.")
        sys.exit(1)
    
    # Define analysis steps
    steps = [
        {
            "name": "Step 1 - Model Analysis",
            "command": "python model_loader.py",
            "description": "Load model and extract architecture metrics"
        },
        {
            "name": "Step 2 - Dataset Analysis", 
            "command": "python dataset_visualizer.py",
            "description": "Analyze dataset with polygon annotations"
        },
        {
            "name": "Step 3 - Model Evaluation & XAI",
            "command": "python model_evaluation_xai.py", 
            "description": "Evaluate model performance and run XAI analysis"
        },
        {
            "name": "Step 4 - Results Visualization",
            "command": "python visualize_xai_results.py",
            "description": "Generate visualizations and comprehensive report"
        }
    ]
    
    # Run each step
    failed_steps = []
    total_start_time = time.time()
    
    for i, step in enumerate(steps, 1):
        print_header(f"{step['name']} ({i}/{len(steps)})", char="-", width=60)
        
        success = run_step(step['name'], step['command'], step['description'])
        
        if not success:
            failed_steps.append(step['name'])
            print(f"⚠️  Continuing with remaining steps...")
    
    total_end_time = time.time()
    
    # Final summary
    print_header("ANALYSIS COMPLETE")
    
    print(f"⏱️  Total execution time: {total_end_time - total_start_time:.1f} seconds")
    
    if not failed_steps:
        print("🎉 All analysis steps completed successfully!")
    else:
        print(f"⚠️  {len(failed_steps)} step(s) failed:")
        for step in failed_steps:
            print(f"   - {step}")
    
    # Show generated files
    print(f"\n📁 Generated Files:")
    output_files = [
        "dysplacianet_model_analysis.json",
        "model_evaluation_results.json", 
        "xai_analysis_results.json",
        "xai_visualizations/",
        "COMPREHENSIVE_ANALYSIS_SUMMARY.md"
    ]
    
    for file_path in output_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (not found)")
    
    # Show key results
    print(f"\n📊 Key Results Summary:")
    
    try:
        import json
        
        # Load evaluation results
        if Path("model_evaluation_results.json").exists():
            with open("model_evaluation_results.json", "r") as f:
                eval_results = json.load(f)
            
            print(f"   🎯 Model Accuracy: {eval_results['accuracy']:.4f} ({eval_results['accuracy']*100:.2f}%)")
            print(f"   📈 Precision: {eval_results['precision']:.4f}")
            print(f"   📈 Recall: {eval_results['recall']:.4f}")
            print(f"   📈 F1-Score: {eval_results['f1_score']:.4f}")
        
        # Load XAI results
        if Path("xai_analysis_results.json").exists():
            with open("xai_analysis_results.json", "r") as f:
                xai_results = json.load(f)
            
            gc_faith = xai_results['summary_metrics']['gradcam']['avg_faithfulness']
            ig_faith = xai_results['summary_metrics']['integrated_gradients']['avg_faithfulness']
            gc_local = xai_results['summary_metrics']['gradcam']['avg_localization']
            ig_local = xai_results['summary_metrics']['integrated_gradients']['avg_localization']
            
            print(f"   🔮 Grad-CAM Faithfulness: {gc_faith:.4f}")
            print(f"   🔮 Integrated Gradients Faithfulness: {ig_faith:.4f}")
            print(f"   🎯 Grad-CAM Localization: {gc_local:.4f}")
            print(f"   🎯 Integrated Gradients Localization: {ig_local:.4f}")
            
            print(f"\n🏆 Best Methods:")
            print(f"   Faithfulness: {'Grad-CAM' if gc_faith > ig_faith else 'Integrated Gradients'}")
            print(f"   Localization: {'Grad-CAM' if gc_local > ig_local else 'Integrated Gradients'}")
    
    except Exception as e:
        print(f"   ⚠️  Could not load results summary: {e}")
    
    # Next steps
    print(f"\n🚀 Next Steps:")
    print(f"   1. Review the comprehensive analysis summary: COMPREHENSIVE_ANALYSIS_SUMMARY.md")
    print(f"   2. Examine XAI visualizations in: xai_visualizations/")
    print(f"   3. Analyze detailed results in the JSON files")
    print(f"   4. Consider model improvements based on findings")
    print(f"   5. Validate explanations with domain experts")
    
    print(f"\n" + "="*80)
    print("🎯 Analysis workflow completed!")
    print("="*80)


if __name__ == "__main__":
    main()
