/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// DEPRECATED: The headers in include/grpc++ are deprecated. Please include the
// headers in include/grpcpp instead. This header exists only for backwards
// compatibility.

#ifndef GRPCXX_IMPL_CODEGEN_SECURITY_AUTH_CONTEXT_H
#define GRPCXX_IMPL_CODEGEN_SECURITY_AUTH_CONTEXT_H

#include <grpcpp/impl/codegen/security/auth_context.h>

#endif  // GRPCXX_IMPL_CODEGEN_SECURITY_AUTH_CONTEXT_H
