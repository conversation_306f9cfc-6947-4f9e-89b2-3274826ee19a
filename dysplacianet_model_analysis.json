{"basic": {"model_name": "DysplaciaNet", "input_shape": [null, 299, 299, 3], "output_shape": [null, 1], "total_params": 72977, "trainable_params": 72977, "non_trainable_params": 0, "total_layers": 18, "model_type": "Binary Classification", "task": "Dysplastic vs Normal Cell Classification"}, "layers": {"layers": [{"layer_index": 0, "layer_name": "conv2d", "layer_type": "Conv2D", "output_shape": "(None, 297, 297, 16)", "param_count": 448, "trainable": true, "activation": "linear", "filters": 16, "kernel_size": [3, 3], "strides": [1, 1], "padding": "valid"}, {"layer_index": 1, "layer_name": "activation", "layer_type": "Activation", "output_shape": "(None, 297, 297, 16)", "param_count": 0, "trainable": true, "activation": "relu"}, {"layer_index": 2, "layer_name": "max_pooling2d", "layer_type": "MaxPooling2D", "output_shape": "(None, 148, 148, 16)", "param_count": 0, "trainable": true, "strides": [2, 2], "padding": "valid"}, {"layer_index": 3, "layer_name": "conv2d_1", "layer_type": "Conv2D", "output_shape": "(None, 146, 146, 16)", "param_count": 2320, "trainable": true, "activation": "linear", "filters": 16, "kernel_size": [3, 3], "strides": [1, 1], "padding": "valid"}, {"layer_index": 4, "layer_name": "activation_1", "layer_type": "Activation", "output_shape": "(None, 146, 146, 16)", "param_count": 0, "trainable": true, "activation": "relu"}, {"layer_index": 5, "layer_name": "max_pooling2d_1", "layer_type": "MaxPooling2D", "output_shape": "(None, 73, 73, 16)", "param_count": 0, "trainable": true, "strides": [2, 2], "padding": "valid"}, {"layer_index": 6, "layer_name": "conv2d_2", "layer_type": "Conv2D", "output_shape": "(None, 71, 71, 16)", "param_count": 2320, "trainable": true, "activation": "linear", "filters": 16, "kernel_size": [3, 3], "strides": [1, 1], "padding": "valid"}, {"layer_index": 7, "layer_name": "activation_2", "layer_type": "Activation", "output_shape": "(None, 71, 71, 16)", "param_count": 0, "trainable": true, "activation": "relu"}, {"layer_index": 8, "layer_name": "max_pooling2d_2", "layer_type": "MaxPooling2D", "output_shape": "(None, 35, 35, 16)", "param_count": 0, "trainable": true, "strides": [2, 2], "padding": "valid"}, {"layer_index": 9, "layer_name": "conv2d_3", "layer_type": "Conv2D", "output_shape": "(None, 33, 33, 16)", "param_count": 2320, "trainable": true, "activation": "linear", "filters": 16, "kernel_size": [3, 3], "strides": [1, 1], "padding": "valid"}, {"layer_index": 10, "layer_name": "activation_3", "layer_type": "Activation", "output_shape": "(None, 33, 33, 16)", "param_count": 0, "trainable": true, "activation": "relu"}, {"layer_index": 11, "layer_name": "max_pooling2d_3", "layer_type": "MaxPooling2D", "output_shape": "(None, 16, 16, 16)", "param_count": 0, "trainable": true, "strides": [2, 2], "padding": "valid"}, {"layer_index": 12, "layer_name": "flatten", "layer_type": "<PERSON><PERSON>", "output_shape": "(None, 4096)", "param_count": 0, "trainable": true}, {"layer_index": 13, "layer_name": "dense", "layer_type": "<PERSON><PERSON>", "output_shape": "(None, 16)", "param_count": 65552, "trainable": true, "activation": "linear"}, {"layer_index": 14, "layer_name": "activation_4", "layer_type": "Activation", "output_shape": "(None, 16)", "param_count": 0, "trainable": true, "activation": "relu"}, {"layer_index": 15, "layer_name": "dropout", "layer_type": "Dropout", "output_shape": "(None, 16)", "param_count": 0, "trainable": true}, {"layer_index": 16, "layer_name": "dense_1", "layer_type": "<PERSON><PERSON>", "output_shape": "(None, 1)", "param_count": 17, "trainable": true, "activation": "linear"}, {"layer_index": 17, "layer_name": "activation_5", "layer_type": "Activation", "output_shape": "(None, 1)", "param_count": 0, "trainable": true, "activation": "sigmoid"}], "layer_type_counts": {"Conv2D": 4, "Activation": 6, "MaxPooling2D": 4, "Flatten": 1, "Dense": 2, "Dropout": 1}, "total_layers": 18}}