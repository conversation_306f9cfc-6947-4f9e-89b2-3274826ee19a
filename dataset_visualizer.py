#!/usr/bin/env python3
"""
DysplaciaNet Dataset Visualizer

This script provides comprehensive visualization capabilities for the DysplaciaNet dataset,
including image display with annotations, dataset statistics, and classification analysis.

Author: AI Assistant
Date: 2025
"""

import os
import json
import numpy as np
from pathlib import Path
from PIL import Image
from typing import Dict, List, Tuple, Optional
import warnings

warnings.filterwarnings('ignore')

class DysplaciaNetDatasetVisualizer:
    """
    A comprehensive dataset visualizer for DysplaciaNet dataset.
    
    This class provides functionality to:
    - Load and parse image and annotation data
    - Visualize images with their annotations
    - Generate dataset statistics and distributions
    - Create comprehensive dataset analysis reports
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the dataset visualizer.
        
        Args:
            data_dir (str): Directory containing dataset images and annotations
        """
        self.data_dir = Path(data_dir)
        self.dataset_info = {}
        self.images_data = []
        
        # Classification mapping based on filename prefixes
        self.class_mapping = {
            'TD': 'Tesis Displasica (Dysplastic)',
            'TN': 'Tesis Normal (Normal)',
            'WD': 'Wrong Classification - Actually Dysplastic',
            'WN': 'Wrong Classification - Actually Normal',
            'ZD': 'Correct Classification - Dysplastic',
            'ZN': 'Correct Classification - Normal'
        }
        
    def load_dataset(self) -> bool:
        """
        Load all images and their corresponding annotations from the dataset.
        
        Returns:
            bool: True if dataset loaded successfully, False otherwise
        """
        try:
            print("🔄 Loading dataset...")
            
            if not self.data_dir.exists():
                print(f"❌ Data directory not found: {self.data_dir}")
                return False
            
            # Find all image files
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
            image_files = []
            
            for ext in image_extensions:
                image_files.extend(list(self.data_dir.glob(f"*{ext}")))
                image_files.extend(list(self.data_dir.glob(f"*{ext.upper()}")))
            
            if not image_files:
                print("❌ No image files found in the dataset directory.")
                return False
            
            print(f"📁 Found {len(image_files)} image files")
            
            # Load each image with its annotation
            for img_path in image_files:
                # Look for corresponding annotation file
                annotation_path = self.data_dir / f"{img_path.stem}_json.txt"
                
                if annotation_path.exists():
                    image_data = self._load_single_image_data(img_path, annotation_path)
                    if image_data:
                        self.images_data.append(image_data)
                else:
                    print(f"⚠️  No annotation found for {img_path.name}")
            
            print(f"✅ Successfully loaded {len(self.images_data)} images with annotations")
            self._analyze_dataset()
            return True
            
        except Exception as e:
            print(f"❌ Error loading dataset: {str(e)}")
            return False
    
    def _load_single_image_data(self, img_path: Path, annotation_path: Path) -> Optional[Dict]:
        """
        Load a single image and its annotation data.
        
        Args:
            img_path (Path): Path to image file
            annotation_path (Path): Path to annotation file
            
        Returns:
            Optional[Dict]: Image data dictionary or None if error
        """
        try:
            # Load image
            image = Image.open(img_path)
            
            # Load annotation
            with open(annotation_path, 'r') as f:
                annotation = json.load(f)
            
            # Extract classification info from filename
            filename = img_path.stem
            prefix = filename.split('_')[0]
            
            # Determine actual and predicted classes
            actual_class = self._get_actual_class(prefix)
            predicted_class = self._get_predicted_class(prefix)
            is_correct = self._is_correct_prediction(prefix)
            
            image_data = {
                'filename': img_path.name,
                'image_path': str(img_path),
                'annotation_path': str(annotation_path),
                'image': image,
                'annotation': annotation,
                'prefix': prefix,
                'actual_class': actual_class,
                'predicted_class': predicted_class,
                'is_correct': is_correct,
                'classification_type': self.class_mapping.get(prefix, 'Unknown'),
                'image_size': image.size,
                'shapes_count': len(annotation.get('shapes', [])),
                'lobes': annotation.get('lobes', []),
                'cell_status': annotation.get('cellstatus', [])
            }
            
            return image_data
            
        except Exception as e:
            print(f"❌ Error loading {img_path.name}: {str(e)}")
            return None
    
    def _get_actual_class(self, prefix: str) -> str:
        """Get the actual class based on filename prefix."""
        if prefix in ['TD', 'WD', 'ZD']:
            return 'Dysplastic'
        elif prefix in ['TN', 'WN', 'ZN']:
            return 'Normal'
        return 'Unknown'
    
    def _get_predicted_class(self, prefix: str) -> str:
        """Get the predicted class based on filename prefix."""
        if prefix in ['TD', 'ZD']:
            return 'Dysplastic'
        elif prefix in ['TN', 'ZN']:
            return 'Normal'
        elif prefix == 'WD':
            return 'Normal'  # Wrongly predicted as Normal
        elif prefix == 'WN':
            return 'Dysplastic'  # Wrongly predicted as Dysplastic
        return 'Unknown'
    
    def _is_correct_prediction(self, prefix: str) -> bool:
        """Check if the prediction was correct based on filename prefix."""
        return prefix in ['TD', 'TN', 'ZD', 'ZN']
    
    def _analyze_dataset(self):
        """Analyze the loaded dataset and generate statistics."""
        if not self.images_data:
            return
        
        print("📊 Analyzing dataset...")
        
        # Basic statistics
        total_images = len(self.images_data)
        correct_predictions = sum(1 for img in self.images_data if img['is_correct'])
        
        # Class distributions
        actual_classes = [img['actual_class'] for img in self.images_data]
        predicted_classes = [img['predicted_class'] for img in self.images_data]

        # Classification type distribution
        classification_types = [img['classification_type'] for img in self.images_data]

        # Count distributions manually
        def count_values(values):
            counts = {}
            for value in values:
                counts[value] = counts.get(value, 0) + 1
            return counts

        self.dataset_info = {
            'total_images': total_images,
            'correct_predictions': correct_predictions,
            'accuracy': correct_predictions / total_images if total_images > 0 else 0,
            'actual_class_distribution': count_values(actual_classes),
            'predicted_class_distribution': count_values(predicted_classes),
            'classification_type_distribution': count_values(classification_types)
        }
    
    def display_sample_info(self, num_samples: int = 6):
        """
        Display information about sample images from the dataset.

        Args:
            num_samples (int): Number of sample images to display info for
        """
        if not self.images_data:
            print("❌ No dataset loaded. Please load dataset first.")
            return

        print(f"🖼️  Sample Images Information ({num_samples} samples):")
        print("=" * 80)

        # Select random samples
        sample_indices = np.random.choice(len(self.images_data),
                                        min(num_samples, len(self.images_data)),
                                        replace=False)

        for i, idx in enumerate(sample_indices):
            img_data = self.images_data[idx]

            print(f"\n📸 Sample {i+1}:")
            print(f"   Filename: {img_data['filename']}")
            print(f"   Actual Class: {img_data['actual_class']}")
            print(f"   Predicted Class: {img_data['predicted_class']}")
            print(f"   Classification: {'✅ Correct' if img_data['is_correct'] else '❌ Wrong'}")
            print(f"   Image Size: {img_data['image_size']}")
            print(f"   Shapes Count: {img_data['shapes_count']}")
            print(f"   Cell Status: {img_data['cell_status']}")
            print(f"   Lobes: {img_data['lobes']}")

        print("=" * 80)
    
    def print_detailed_statistics(self):
        """
        Print detailed dataset statistics and distributions.
        """
        if not self.dataset_info:
            print("❌ No dataset statistics available. Please load dataset first.")
            return

        print("📊 Detailed Dataset Statistics:")
        print("=" * 80)

        # Basic stats
        info = self.dataset_info
        accuracy = info['accuracy']
        correct = info['correct_predictions']
        total = info['total_images']
        incorrect = total - correct

        print(f"\n📈 ACCURACY ANALYSIS:")
        print(f"   Total Images: {total}")
        print(f"   Correct Predictions: {correct} ({(correct/total)*100:.1f}%)")
        print(f"   Incorrect Predictions: {incorrect} ({(incorrect/total)*100:.1f}%)")
        print(f"   Overall Accuracy: {accuracy:.2%}")

        # Class distributions comparison
        actual_dist = info['actual_class_distribution']
        predicted_dist = info['predicted_class_distribution']

        print(f"\n📊 CLASS DISTRIBUTION COMPARISON:")
        print(f"   {'Class':<15} {'Actual':<10} {'Predicted':<10} {'Difference':<10}")
        print(f"   {'-'*15} {'-'*10} {'-'*10} {'-'*10}")

        all_classes = set(list(actual_dist.keys()) + list(predicted_dist.keys()))
        for cls in sorted(all_classes):
            actual_count = actual_dist.get(cls, 0)
            predicted_count = predicted_dist.get(cls, 0)
            diff = predicted_count - actual_count
            print(f"   {cls:<15} {actual_count:<10} {predicted_count:<10} {diff:+<10}")

        # Classification type breakdown
        class_type_dist = info['classification_type_distribution']
        print(f"\n🔍 CLASSIFICATION TYPE BREAKDOWN:")
        for cls_type, count in class_type_dist.items():
            percentage = (count / total) * 100
            print(f"   {cls_type}: {count} ({percentage:.1f}%)")

        print("=" * 80)
    
    def print_dataset_summary(self):
        """Print a comprehensive dataset summary."""
        if not self.dataset_info:
            print("❌ No dataset information available. Please load dataset first.")
            return
        
        print("\n" + "="*80)
        print("📊 DYSPLACIANET DATASET SUMMARY")
        print("="*80)
        
        info = self.dataset_info
        
        print(f"\n📋 BASIC STATISTICS:")
        print(f"   Total Images: {info['total_images']}")
        print(f"   Correct Predictions: {info['correct_predictions']}")
        print(f"   Incorrect Predictions: {info['total_images'] - info['correct_predictions']}")
        print(f"   Overall Accuracy: {info['accuracy']:.2%}")
        
        print(f"\n🎯 ACTUAL CLASS DISTRIBUTION:")
        for cls, count in info['actual_class_distribution'].items():
            percentage = (count / info['total_images']) * 100
            print(f"   {cls}: {count} ({percentage:.1f}%)")
        
        print(f"\n🔮 PREDICTED CLASS DISTRIBUTION:")
        for cls, count in info['predicted_class_distribution'].items():
            percentage = (count / info['total_images']) * 100
            print(f"   {cls}: {count} ({percentage:.1f}%)")
        
        print(f"\n📂 CLASSIFICATION TYPE BREAKDOWN:")
        for cls_type, count in info['classification_type_distribution'].items():
            percentage = (count / info['total_images']) * 100
            print(f"   {cls_type}: {count} ({percentage:.1f}%)")
        
        print("\n" + "="*80)


def main():
    """
    Main function to demonstrate the dataset visualizer functionality.
    """
    print("🚀 DysplaciaNet Dataset Visualizer")
    print("="*50)
    
    # Initialize visualizer
    visualizer = DysplaciaNetDatasetVisualizer()
    
    # Load dataset
    if not visualizer.load_dataset():
        print("❌ Failed to load dataset. Exiting.")
        return
    
    # Print dataset summary
    visualizer.print_dataset_summary()

    # Display sample images info
    print("\n🖼️  Displaying sample images information...")
    visualizer.display_sample_info(num_samples=6)

    # Print detailed statistics
    print("\n📊 Generating detailed statistics...")
    visualizer.print_detailed_statistics()

    print("\n✅ Dataset analysis complete!")


if __name__ == "__main__":
    main()
