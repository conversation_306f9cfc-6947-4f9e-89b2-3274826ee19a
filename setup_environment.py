#!/usr/bin/env python3
"""
DysplaciaNet Environment Setup Script

This script helps set up the virtual environment and install dependencies
for running DysplaciaNet model analysis.

Author: AI Assistant
Date: 2025
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 6:
        print("❌ Python 3.6+ is required for TensorFlow 2.3.0")
        return False
    
    if version.minor >= 9:
        print("⚠️  Warning: Python 3.9+ may have compatibility issues with TensorFlow 2.3.0")
        print("   Consider using Python 3.7 or 3.8 for best compatibility")
    
    return True

def setup_virtual_environment():
    """Set up the virtual environment."""
    env_name = "dysplacianet_env"
    
    print(f"🏗️  Setting up virtual environment: {env_name}")
    
    # Create virtual environment
    if not run_command(f"python3 -m venv {env_name}", "Creating virtual environment"):
        return False
    
    # Upgrade pip
    if not run_command(f"source {env_name}/bin/activate && pip install --upgrade pip", 
                      "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"source {env_name}/bin/activate && pip install -r requirements_dysplacianet.txt", 
                      "Installing requirements"):
        return False
    
    return True

def verify_installation():
    """Verify that the installation was successful."""
    print("🔍 Verifying installation...")
    
    test_script = """
import tensorflow as tf
import keras
import numpy as np
from PIL import Image
import cv2

print(f"TensorFlow version: {tf.__version__}")
print(f"Keras version: {keras.__version__}")
print(f"NumPy version: {np.__version__}")
print("✅ All core dependencies imported successfully!")
"""
    
    with open("test_imports.py", "w") as f:
        f.write(test_script)
    
    success = run_command("source dysplacianet_env/bin/activate && python test_imports.py", 
                         "Testing imports")
    
    # Clean up test file
    if os.path.exists("test_imports.py"):
        os.remove("test_imports.py")
    
    return success

def print_usage_instructions():
    """Print instructions for using the environment."""
    print("\n" + "="*80)
    print("🎉 SETUP COMPLETE!")
    print("="*80)
    print("\n📋 USAGE INSTRUCTIONS:")
    print("\n1. Activate the virtual environment:")
    print("   source dysplacianet_env/bin/activate")
    print("\n2. Run the model loader:")
    print("   python model_loader.py")
    print("\n3. Run the dataset visualizer:")
    print("   python dataset_visualizer.py")
    print("\n4. Deactivate the environment when done:")
    print("   deactivate")
    print("\n📁 FILES CREATED:")
    print("   - dysplacianet_env/          (virtual environment)")
    print("   - model_loader.py            (model analysis script)")
    print("   - dataset_visualizer.py      (dataset analysis script)")
    print("   - requirements_dysplacianet.txt (dependency list)")
    print("   - dysplacianet_metrics.json  (model metrics output)")
    print("\n🔧 TROUBLESHOOTING:")
    print("   - If you encounter import errors, ensure you've activated the environment")
    print("   - For visualization features, you may need to install matplotlib separately")
    print("   - Check that model files exist in: DysplaciaNet_Interpretability_Study-master/model/")
    print("   - Check that data files exist in: data/")
    print("\n" + "="*80)

def main():
    """Main setup function."""
    print("🚀 DysplaciaNet Environment Setup")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if requirements file exists
    if not Path("requirements_dysplacianet.txt").exists():
        print("❌ requirements_dysplacianet.txt not found!")
        print("   Please ensure this file is in the current directory.")
        sys.exit(1)
    
    # Set up virtual environment
    if not setup_virtual_environment():
        print("❌ Failed to set up virtual environment!")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("❌ Installation verification failed!")
        print("   The environment was created but there may be issues.")
        print("   Try running the scripts manually to debug.")
    
    # Print usage instructions
    print_usage_instructions()

if __name__ == "__main__":
    main()
