# DysplaciaNet Model Evaluation and Visualization

This repository contains scripts to load, evaluate, and visualize the DysplaciaNet model for cervical cell classification.

## Files Overview

### 1. `model_evaluation_and_visualization.py`
A comprehensive evaluation script that provides:
- Model loading and compilation
- Performance metrics calculation
- Confusion matrix visualization
- Prediction distribution analysis
- Annotated example visualization

### 2. `demo_dysplacianet.py`
A simplified demo script that:
- Loads the DysplaciaNet model
- Demonstrates predictions on synthetic data
- Analyzes model layer outputs
- Tests on real images (if provided)

## Model Information

- **Architecture**: Convolutional Neural Network (CNN)
- **Input**: 299×299×3 RGB images
- **Output**: Binary classification (Dysplastic vs Normal cells)
- **Task**: Cervical cell classification for dysplasia detection

## Requirements

Make sure you have the required dependencies installed:

```bash
pip install tensorflow~=2.3.0
pip install keras~=2.4.3
pip install matplotlib~=3.3.4
pip install numpy~=1.19.2
pip install scikit-learn
pip install seaborn
pip install opencv-python~=********
```

## Usage

### Quick Demo (No Images Required)

Run the demo script to see the model in action with synthetic data:

```bash
python demo_dysplacianet.py
```

This will:
1. Load the DysplaciaNet model
2. Show model architecture information
3. Generate synthetic test images and make predictions
4. Analyze model layer outputs
5. Optionally test on a real image if you provide a path

### Comprehensive Evaluation (Requires Images)

For full evaluation with real images:

```bash
python model_evaluation_and_visualization.py
```

When prompted, provide the path to your image folder containing the test images.

### Using the Classes Programmatically

```python
from model_evaluation_and_visualization import DysplaciaNetEvaluator

# Initialize evaluator
evaluator = DysplaciaNetEvaluator()

# Load model
model = evaluator.load_model()

# Make prediction on single image
prob, pred = evaluator.make_prediction('path/to/image.jpg')

# Evaluate on multiple images (if you have a folder)
metrics = evaluator.evaluate_on_examples('path/to/image/folder')

# Generate visualizations
evaluator.plot_confusion_matrix(metrics)
evaluator.visualize_predictions_with_annotations('path/to/image/folder')
```

## Model Files

The scripts expect the following model files to be present:
- `model/final_model.json` - Model architecture
- `model/final_model_weights.h5` - Trained weights

## Example Images

The evaluation script includes predefined example images from the original study:
- Dysplastic cell images (TD_, ZD_ prefixes)
- Normal cell images (TN_, ZN_, WN_ prefixes)
- Mixed classification images (WD_ prefix)

## Output Metrics

The evaluation provides:
- **Accuracy**: Overall classification accuracy
- **Precision**: Precision for normal cell detection
- **Recall**: Recall for normal cell detection  
- **F1-Score**: Harmonic mean of precision and recall
- **Confusion Matrix**: Detailed classification breakdown
- **Sensitivity/Specificity**: Additional diagnostic metrics

## Visualization Features

### 1. Confusion Matrix
Shows true vs predicted classifications with color-coded heatmap.

### 2. Prediction Distribution
Histograms and scatter plots showing:
- Distribution of prediction probabilities
- Separation between classes
- Decision threshold visualization

### 3. Annotated Examples
Grid display showing:
- Original images
- True vs predicted labels
- Confidence scores
- Annotation scores from experts
- Color-coded correctness (green=correct, red=incorrect)

### 4. Model Layer Analysis
Visualization of intermediate layer outputs and activations.

## Interpretation Guide

### Prediction Values
- **Probability < 0.5**: Classified as Dysplastic (abnormal)
- **Probability > 0.5**: Classified as Normal
- **Confidence**: Distance from 0.5 threshold

### Annotation Scores
- **Positive scores (+1 to +7)**: Indicate dysplastic characteristics
- **Negative scores (-1 to -7)**: Indicate normal characteristics
- **Score 0**: Borderline/uncertain cases

### Color Coding
- **Red**: Dysplastic predictions or incorrect classifications
- **Blue/Green**: Normal predictions or correct classifications
- **Black dashed line**: Decision threshold (0.5)

## Troubleshooting

### Common Issues

1. **Model files not found**
   - Ensure `model/final_model.json` and `model/final_model_weights.h5` exist
   - Check file paths are correct

2. **Image loading errors**
   - Verify image paths are correct
   - Ensure images are in supported formats (jpg, png)
   - Check image folder structure

3. **Memory issues**
   - Reduce number of examples in visualization
   - Process images in smaller batches

4. **Dependency errors**
   - Install required packages with correct versions
   - Use virtual environment to avoid conflicts

### Performance Notes

- Model expects 299×299 pixel images
- Images are automatically resized and normalized
- Prediction time: ~50-100ms per image on CPU
- GPU acceleration recommended for batch processing

## Extending the Scripts

You can easily extend these scripts to:
- Add new visualization types
- Implement different evaluation metrics
- Process different image formats
- Add data augmentation
- Compare with other models

## Contact

For questions about the model or evaluation scripts, refer to the original DysplaciaNet research paper or the interpretability study documentation.
